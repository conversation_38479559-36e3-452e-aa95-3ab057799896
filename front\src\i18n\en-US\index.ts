export default {
  app: {
    english: 'English',
    chinese: '中文',
  },
  changeLanguage: 'Change Language',
  success: 'Success',
  failed: 'Failed',
  all: 'All',
  hint: 'Hint',
  optional: 'Optional',
  open: 'Open',
  close: 'Close',
  confirm: 'Confirm',
  logoutConfirm: 'Are you sure you want to logout?',
  cancel: 'Cancel',
  add: 'Add',
  disabled: 'Disabled',
  confirmDelete: 'Are you sure you want to delete this item?',
  error: {
    required: 'The field id required.',
    passwordNotMatch: 'Password not match.',
    max: 'The field may not be greater than {max} characters.',
    min: 'The field must be at least {min} characters.',
    maxNumber: 'The field may not be greater than {max}.',
    minNumber: 'The field must be at least {min}.',
    betweenNumber: 'The field must be between {min} and {max}.',
    email: 'Invalid email format.',
    noData: 'No data available.',
    noImage: 'No image available.',
    invalidDate: 'Invalid date format.',
  },
  pending: 'Pending',
  processing: 'Processing',
  completed: 'Completed',
  cancelled: 'Cancelled',
  'wc-pending': 'Pending',
  'wc-processing': 'Processing',
  'wc-on-hold': 'On Hold',
  'wc-completed': 'Completed',
  'wc-cancelled': 'Cancelled',
  'wc-refunded': 'Refunded',
  'wc-failed': 'Failed',
  onsite: 'Onsite',
  online: 'Online',
  orderDetail: 'Order Detail',
  attendance: {
    label: 'Attendance',
    history: 'Attendance History',
  },
  payroll: {
    label: 'Payroll',
    period: 'Payroll Period',
    payDate: 'Pay Date',
    basicSalary: 'Basic Salary',
    hourlyRate: 'Hourly Rate',
    workDays: 'Work Days',
    workHours: 'Work Hours',
    income: 'Income',
    bonus: 'Bonus',
    deduction: 'Deduction',
    grossSalary: 'Gross Salary',
    totalDeductions: 'Total Deductions',
    netSalary: 'Net Salary',
    payStatus: 'Pay Status',
    paid: 'Paid',
    unpaid: 'Unpaid',
    status: {
      draft: 'Draft',
      unpaid: 'Unpaid',
      paid: 'Paid',
      void: 'Void',
    },
    // 薪資項目
    // income
    overtime_pay: 'Overtime Pay',
    meal_allowance: 'Meal Allowance',
    transportation_allowance: 'Transportation Allowance',
    position_allowance: 'Position Allowance',
    performance_bonus: 'Performance Bonus',
    perfect_attendance_bonus: 'Perfect Attendance Bonus',
    year_end_bonus: 'Year-end Bonus',
    // deduction
    labor_insurance: 'Labor Insurance',
    health_insurance: 'Health Insurance',
    income_tax: 'Income Tax',
    labor_pension: 'Labor Pension',
    health_pension: 'Health Pension',
    late_deduction: 'Late Deduction',
    absence_deduction: 'Absence Deduction',
  },
  payrolls: 'Payrolls',
  salary: 'Salary',
  salaryType: {
    label: 'Salary Type',
    salary: 'Salary',
    monthly: 'Monthly Salary',
    hourly: 'Hourly Wage',
  },
  order: {
    label: 'Order',
    list: 'Order List',
    history: 'Order History',
    salesStatistics: 'Sales Statistics',
    void: 'Voided Order',
    qty: 'Order Qty',
    count: '{count} order | {count} orders',
    total: 'Total Orders',
    noRecord: 'No order record.',
  },
  orders: 'Orders',
  // user
  user: {
    menu: 'Users',
    label: 'User',
    all: 'All Users',
    list: 'User List',
  },
  username: 'Username',
  account: 'Account ID',
  password: 'Password',
  confirmPassword: 'Confirm Password',
  datePicker: {
    from: 'From Date',
    to: 'To Date',
  },
  // permission
  permissions: 'Permissions',
  permission: {
    view_page_order: 'View Order Page',
    create_order: 'Create Order',
    update_order: 'Update Order',
    delete_order: 'Delete Order',
    void_order: 'Void Order',
    checkout_order: 'Checkout Order',
  },
  name: 'Name',
  type: 'Type',
  email: {
    label: 'Email',
    send: 'Send Email',
    sendAll: 'Send All',
    notSet: 'No Email Set',
  },
  role: 'Role',
  phone: 'Phone',
  licensePlate: 'License Plate',
  country: 'Country',
  state: 'State',
  city: 'City',
  zipcode: 'Zipcode',
  address: 'Address',
  receiver: 'Receiver',
  date: 'Date',
  time: 'Time',
  item: 'Item',
  quantity: 'Qty',
  status: 'Status',
  allStatus: 'All Status',
  note: {
    label: 'Note',
    edit: 'Edit Note',
  },
  group: 'Group',
  catalog: {
    label: 'Catalog',
  },
  product: {
    label: 'Product',
    name: 'Product Name',
    data: 'Product Data',
    all: 'All Products',
  },
  products: 'Products',
  addProduct: 'Add Product',
  category: 'Category',
  barcode: 'Barcode',
  description: 'Description',
  shortDescription: 'Short Description',
  unit: 'Unit',
  price: 'Price',
  salePrice: 'Sale Price',
  flexiblePrice: 'Flexible Price',
  productType: 'Product Type',
  fixedPriceProduct: 'Fixed Price Product',
  flexiblePriceProduct: 'Flexible Price Product',
  setServiceAmount: 'Set Service Amount',
  enterAmountLabel: 'Please enter amount (AU$)',
  cost: 'Cost',
  stock: 'Stock',
  stockQuantity: 'Stock Qty',
  minStockQuantity: 'Min Stock Qty',
  image: 'Image',
  images: 'Images',

  serviceFee: 'Service Fee',
  tax: 'Tax',
  taxID: 'ABN',
  shippingFee: 'Shipping Fee',
  supplier: 'Supplier',

  // Attendance
  clockIn: 'Clock In',
  clock_in: 'Clock In',
  clockOut: 'Clock Out',
  clock_out: 'Clock Out',
  clockType: 'Clock Type',
  clockTime: 'Clock Time',

  createdAt: 'Created At',
  updatedAt: 'Updated At',

  createUser: 'Create User',
  createGroup: 'Create Group',
  createCategory: 'Create Category',
  createProduct: 'Create Product',
  editProduct: 'Edit Product',
  createCustomer: 'Create Customer',
  editCustomer: 'Edit Customer',

  save: 'Save',
  reset: 'Reset',
  clear: 'Clear',
  create: 'Create',
  update: 'Update',
  submit: 'Submit',
  delete: 'Delete',
  rememberMe: 'Remember Me',
  login: 'Login',
  logout: 'Logout',

  // Order
  itemNum: '#Item',
  rebate: 'Rebate',
  discount: 'Discount',
  percentageOff: 'Discount ({discount}% off)',
  checkout: 'Checkout',
  payType: 'Pay Type',
  payment: {
    label: 'Payment',
    stats: 'Payment Statistic',
    cash: 'Cash',
    creditCard: 'Credit Card',
    paypal: 'Paypal',
  },
  cash: 'Cash',
  creditCard: 'Credit Card',
  subtotal: 'Subtotal',
  total: 'Total',
  cashReceived: 'Cash Received',

  guest: 'Guest',
  customerData: 'Customer Data',
  customer: {
    label: 'Customer',
    all: 'All Customers',
    notSupplier: 'The customer is not a supplier.',
  },
  customers: 'Customers',
  checkoutSuccess: 'Checkout Success',
  checkoutSuccessMessage: 'The order has been created successfully. Do you want to print the invoice?',
  checkoutError: {
    noItems: 'Please add at least one item to the cart.',
  },
  printInvoice: 'Print Xero Invoice',
  printInvoiceSuccess: 'Successfully printed Xero invoice',
  printInvoiceError: 'Failed to print Xero invoice',
  sendEmail: 'Send Email',
  sendEmailSuccess: 'Successfully sent email',
  sendEmailError: 'Failed to send email',
  sendEmailNoCustomerOrEmail: 'This order does not have a customer or the customer does not have an email.',
  sendEmailNotSynced: 'This order has not been synced to Xero, cannot send email',
  orderNumber: 'Order Number',

  unknown: {
    customer: 'No Customer Data',
  },

  search: {
    label: 'Search',
    customer: 'Enter customer name, phone, ABN',
    product: 'Enter product name or barcode',
    order: 'Enter order No.',
  },

  punch: 'Punch',
  history: 'History',
  latestPunch: 'Latest Punch',
  changeUser: 'Change User',

  orderNo: 'Order No',
  dateAt: 'Date At',
  orderDate: 'Order Date',
  void: 'Void',
  reason: 'Reason',
  voidReason: 'Void Reason',

  orderVoided: {
    title: 'Order Voided',
    reasons: {
      outOfStock: 'Out of Stock',
      duplicateOrder: 'Duplicate Order',
      incorrectInfo: 'Incorrect Info',
      customerChangedMind: 'Customer Changed Mind',
      customerReject: 'Customer Reject',
      other: 'Other',
    },
    confirm: 'Are you sure you want to void this order?',
    reasonRequired: 'Please select a reason.',
    otherReasonRequired: 'Please enter a reason.',
  },
  orderStatus: {
    label: 'Order Status',
    pending: 'Pending',
    processing: 'Processing',
    completed: 'Completed',
    cancelled: 'Cancelled',
    void: 'Void',
  },

  inventory: {
    label: 'Inventory',
    stockHistory: 'Stock History',
    stock_in: {
      label: 'Stock In',
      caption: 'Add stock to the product',
    },
    return: {
      label: 'Return',
      caption: 'Return stock from customer or to supplier',
    },
    scrap: {
      label: 'Scrap',
      caption: 'Remove stock from inventory',
    },
    stocktaking: {
      label: 'Stocktaking',
      caption: 'Count stock in the store',
    },
    transaction: {
      sale: 'Sale',
      purchase: 'Purchase',
      stock_in: 'Stock In',
      stock_out: 'Stock Out',
      stocktaking: 'Stocktaking',
      scrap: 'Scrap',
      return_in: 'Return In',
      return_out: 'Return Out',
    },
    returnType: {
      label: 'Return Type',
      customer_return: 'Return from Customer',
      supplier_return: 'Return to Supplier',
    },
    beforeQuantity: 'Before Qty',
    afterQuantity: 'After Qty',
    diffQuantity: 'Diff Qty',
  },

  actions: 'Actions',

  wpOrder: {
    actions: {
      complete: 'Complete',
      cancel: 'Cancel',
      refund: 'Refund',
      processing: 'Change to Processing',
    },
    title: {
      complete: 'Complete Order',
      cancel: 'Cancel Order',
      refund: 'Refund Order',
      processing: 'Processing Order',
    },
  },

  orderItem: {
    is_free: 'Free',
  },

  vip: {
    label: 'VIP',
    expiryDate: 'VIP Expiry Date',
  },

  year: {
    label: 'Year',
  },
  month: {
    label: 'Month',
  },

  barcodeScanner: {
    ready: 'Barcode Scanner Ready',
    scanning: 'Scanning...',
    notFound: 'Product Not Found',
    notFoundMessage: 'No product found with barcode {barcode}',
    scanCompleted: 'Scan Completed',
  },
  added: 'Added',

  system: 'System',
  updateService: {
    updateNotification: {
      title: 'System Update Available',
      newVersion: 'New version available',
      currentVersion: 'Current version',
      buildTime: 'Build time',
      recommendUpdate:
        'We recommend updating immediately for the best experience',
      doNotClose: 'Please do not close the browser window during the update',
      updateLater: 'Remind Later',
      updateNow: 'Update Now',
      laterReminder: 'We will remind you to update later',
    },
    updateProcess: {
      updating: 'Updating system, please wait...',
      success: 'System update completed, reloading soon',
      failed: 'Update failed, attempting force refresh',
      forceRefresh: 'Force refreshing...',
    },
  },
  xero: {
    menu: {
      setup: 'Setup',
      invoices: 'Invoices',
    },
    setup: {
      title: 'Xero API Setup',
      connectionStatus: {
        connected: 'Connected to Xero account: {tenantName}',
        tokenExpiry: 'Token expires at: {expiryDate}',
        notConnected: 'Not connected to Xero account',
        disconnect: 'Disconnect',
        refreshToken: 'Refresh Token',
      },
      form: {
        clientId: 'Client ID',
        clientSecret: 'Client Secret',
        redirectUri: 'Redirect URI',
        scopes: 'Scopes',
        scopesHint: 'Default: accounting.transactions accounting.contacts',
        scopesPlaceholder: 'accounting.transactions accounting.contacts',
        saveConfig: 'Save Configuration',
        connectXero: 'Connect to Xero',
      },
      validation: {
        clientIdRequired: 'Client ID is required',
        clientSecretRequired: 'Client Secret is required',
        redirectUriRequired: 'Redirect URI is required',
      },
      dialog: {
        disconnectTitle: 'Confirm Disconnect',
        disconnectMessage: 'Are you sure you want to disconnect from Xero?',
      },
      error: {
        stateStorageFailed:
          'Failed to save authentication state. Please try again.',
      },
    },
    redirect: {
      processing: 'Processing Xero authentication...',
      success: {
        title: 'Xero Connection Successful!',
        connectedTo: 'Successfully connected to: {tenantName}',
        backToSettings: 'Back to Settings',
        notification: 'Xero connection successful!',
      },
      error: {
        title: 'Connection Failed',
        retry: 'Try Again',
        clearState: 'Clear State & Retry',
        messages: {
          authError: 'Authentication error: {error}',
          missingParams: 'Missing required authentication parameters',
          invalidState: 'Invalid state parameter, possible security risk',
          noSavedState: 'No saved state found, please try connecting again',
          unknownOrg: 'Unknown Organization',
        },
      },
      debug: {
        title: 'Debug Information',
        stateCleared: 'Authentication state cleared successfully',
      },
    },
    invoices: {
      title: 'Xero Invoices',
      notConnected:
        'Not connected to Xero. Please configure the connection first.',
      goToSetup: 'Go to Setup',
      dateFrom: 'Date From',
      dateTo: 'Date To',
      invoiceNumber: 'Invoice Number',
      contact: 'Contact',
      date: 'Date',
      dueDate: 'Due Date',
      subTotal: 'Sub Total',
      totalTax: 'Total Tax',
      total: 'Total',
      invoiceDetails: 'Invoice Details',
      basicInfo: 'Basic Information',
      lineItems: 'Line Items',
      unitAmount: 'Unit Amount',
      lineAmount: 'Line Amount',
      enterEmailAddress: 'Please enter email address',
      invoiceNotFound: 'Invoice not found',
      type: {
        label: 'Type',
        accrec: 'Sales Invoice',
        accpay: 'Purchase Invoice',
      },
      status: {
        draft: 'Draft',
        submitted: 'Submitted',
        authorised: 'Authorised',
        paid: 'Paid',
        voided: 'Voided',
        deleted: 'Deleted',
      },
    },
    syncStatus: 'Xero Sync Status',
    notSynced: 'Not Synced',
    syncPending: 'Sync Pending',
    syncing: 'Syncing',
    syncSuccess: 'Sync Success',
    syncFailed: 'Sync Failed',
    voidedInvoice: 'Voided Invoice',
  },
};
