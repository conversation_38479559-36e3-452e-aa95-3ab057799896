# WooCommerce 訂單 API 改進版

## 概述

基於 wcapi.php 的查詢結構，我們已經改進了 WooCommerce 訂單 API，使其更符合標準的 WooCommerce 查詢模式。

## 主要改進

### 1. 動態表前綴檢測
- 自動檢測 WordPress 資料庫的表前綴（wp_, wpdb_, wordpress_, 或無前綴）
- 支援不同的 WordPress 安裝配置

### 2. 標準化查詢結構
參考 wcapi.php 的查詢模式：
- 使用 LEFT JOIN 獲取訂單元數據
- 使用 CASE WHEN 語句優化查詢性能
- 支援狀態格式轉換（pending → wc-pending）

### 3. 完整的訂單資料
獲取更完整的訂單資訊：
- 基本訂單資訊（ID、日期、狀態）
- 客戶資訊（姓名、郵箱、電話、地址）
- 訂單金額（總額、稅額、運費）
- 付款資訊（付款方式、交易ID）

## API 端點

### 1. 診斷端點
```
GET /api/v1/wc-orders/diagnose
```
檢查：
- 資料庫連接狀態
- 表前綴檢測結果
- 訂單數量統計
- 訂單狀態分佈
- 最近訂單列表

### 2. 標準查詢端點（新增）
```
GET /api/v1/wc-orders/standard?limit=20&offset=0&status=pending
```
參數：
- `limit`: 返回數量限制（默認20，最大100）
- `offset`: 偏移量（用於分頁）
- `status`: 訂單狀態過濾

### 3. 歷史訂單
```
GET /api/v1/wc-orders/history
```
使用標準查詢獲取所有訂單，如果失敗則回退到原始方法。

### 4. 待處理訂單
```
GET /api/v1/wc-orders/pending
```
使用標準查詢獲取 pending 狀態的訂單。

### 5. 訂單詳情
```
GET /api/v1/wc-orders/{id}
```
使用完整的 JOIN 查詢獲取訂單的所有詳細資訊。

### 6. 更新訂單狀態
```
PATCH /api/v1/wc-orders/{id}/status
```
支援狀態格式自動轉換（pending → wc-pending）。

## 查詢優化

### 1. 訂單列表查詢
```sql
SELECT 
    p.ID as id,
    p.post_date as date_created,
    p.post_modified as date_modified,
    p.post_status as status,
    MAX(CASE WHEN pm.meta_key = '_order_total' THEN pm.meta_value END) as total,
    MAX(CASE WHEN pm.meta_key = '_billing_first_name' THEN pm.meta_value END) as billing_first_name,
    MAX(CASE WHEN pm.meta_key = '_billing_last_name' THEN pm.meta_value END) as billing_last_name,
    MAX(CASE WHEN pm.meta_key = '_billing_email' THEN pm.meta_value END) as billing_email,
    MAX(CASE WHEN pm.meta_key = '_payment_method_title' THEN pm.meta_value END) as payment_method_title,
    MAX(CASE WHEN pm.meta_key = '_customer_user' THEN pm.meta_value END) as customer_id,
    MAX(CASE WHEN pm.meta_key = '_order_currency' THEN pm.meta_value END) as currency
FROM 
    {prefix}posts p
LEFT JOIN {prefix}postmeta pm ON p.ID = pm.post_id
WHERE 
    p.post_type = 'shop_order'
    AND p.post_status NOT IN ('auto-draft', 'trash')
GROUP BY p.ID 
ORDER BY p.post_date DESC
```

### 2. 訂單詳情查詢
使用多個 LEFT JOIN 獲取所有相關的元數據：
```sql
SELECT 
    p.ID as id,
    p.post_date as date_created,
    p.post_modified as date_modified,
    p.post_status as status,
    COALESCE(pm_total.meta_value, '0') as total,
    COALESCE(pm_billing_first_name.meta_value, '') as billing_first_name,
    -- ... 更多字段
FROM 
    {prefix}posts p
LEFT JOIN {prefix}postmeta pm_total ON p.ID = pm_total.post_id AND pm_total.meta_key = '_order_total'
LEFT JOIN {prefix}postmeta pm_billing_first_name ON p.ID = pm_billing_first_name.post_id AND pm_billing_first_name.meta_key = '_billing_first_name'
-- ... 更多 JOIN
WHERE 
    p.ID = ? AND p.post_type = 'shop_order'
```

## 錯誤處理

### 1. 回退機制
如果標準查詢失敗，自動回退到原始查詢方法。

### 2. 狀態格式處理
自動處理不同的狀態格式：
- `pending` → `wc-pending`
- `processing` → `wc-processing`
- 保留原有的 `wc-` 前綴狀態

### 3. 資料類型轉換
安全地轉換字符串到數值類型，避免轉換錯誤。

## 測試建議

### 1. 先測試診斷端點
```bash
curl -X GET "http://localhost:8080/api/v1/wc-orders/diagnose" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 2. 測試標準查詢
```bash
curl -X GET "http://localhost:8080/api/v1/wc-orders/standard?limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. 測試狀態過濾
```bash
curl -X GET "http://localhost:8080/api/v1/wc-orders/standard?status=pending" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 與 wcapi.php 的對應關係

| wcapi.php action | 新的 API 端點 | 說明 |
|------------------|---------------|------|
| `get_orders` | `/v1/wc-orders/standard` | 標準訂單列表查詢 |
| `get_history_orders` | `/v1/wc-orders/history` | 歷史訂單 |
| `check_new_orders` | `/v1/wc-orders/pending` | 待處理訂單 |
| `get_order_detail` | `/v1/wc-orders/{id}` | 訂單詳情 |
| `update_status` | `PATCH /v1/wc-orders/{id}/status` | 更新狀態 |

這個改進版本應該能更好地與您的 WordPress WooCommerce 資料庫整合，並提供與 wcapi.php 相似的功能。
