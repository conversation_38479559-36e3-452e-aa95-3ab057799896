<template>
  <q-page padding class="row q-pa-none">
    <!-- left side (cart) -->
    <q-card square class="col-3">
      <div class="column full-height">
        <!-- order info -->
        <div class="col-2 col-md-1 order-info">
          <q-item class="full-height q-pa-none">
            <q-item-section side class="q-pr-sm">
              <q-btn
                type="button"
                @click="goBack"
                flat
                unelevated
                :size="$q.screen.lt.md ? 'sm' : 'lg'"
                class="full-height q-pl-sm q-pr-none"
              >
                <q-icon name="arrow_back_ios" />
              </q-btn>
            </q-item-section>

            <q-item-section>
              <!-- Order No -->
              <q-item-label class="text-subtitle1 text-bold">
                No. {{ order.order_no.slice(-3) }}
              </q-item-label>
              <!-- Order Date & Time -->
              <q-item-label
                class="text-subtitle1 cursor-pointer"
                @click="showDatePickerDialog"
              >
                <q-icon name="calendar_month" size="xs" class="q-mr-sm" />
                {{ formatDate(order.order_at, 'YYYY/MM/DD HH:mm') }}
                <q-icon name="edit" size="xs" class="q-ml-sm sm-hide" />
              </q-item-label>

              <!-- Date & Time Picker Dialog -->
              <q-dialog v-model="datePickerDialog">
                <q-date
                  v-model="selectedDate"
                  mask="YYYY/MM/DD"
                  @update:model-value="updateOrderDate"
                  style="max-height: 100%"
                />
                <q-time
                  v-model="selectedTime"
                  mask="HH:mm"
                  @update:model-value="updateOrderTime"
                  format24h
                  style="max-height: 100%"
                />
              </q-dialog>
            </q-item-section>
          </q-item>
        </div>
        <!-- order items -->
        <div class="col-10 col-md-11 bg-cream">
          <div class="column full-height">
            <div class="col-auto order-info">
              <!-- customer -->
              <q-item class="q-py-none">
                <q-item-section @click="showCustomerDialog">
                  <q-item-label class="text-subtitle1 text-bold">
                    <!-- vip -->
                    <span
                      class="text-subtitle1 text-negative text-bold text-center q-px-xs q-mr-xs"
                      style="border: 2px solid #c10015"
                      v-if="currentCustomer?.is_vip"
                    >
                      VIP
                    </span>
                    <q-icon name="person" class="q-mr-sm" v-else />
                    <span v-if="order.customer_uuid">
                      {{ order.customer_name }}
                    </span>
                    <span v-else>
                      <!-- 一般顧客 -->
                      {{ t('guest') }}
                    </span>
                    <q-icon name="edit" class="q-ml-sm" />
                  </q-item-label>
                </q-item-section>
              </q-item>
              <CustomerDialog
                v-model="customerDialog"
                :selected-customer-id="order.customer_uuid"
                @select="onSelectCustomer"
              />
              <!-- cart info -->
              <div class="text-right q-px-md">
                <span class="q-mr-md">
                  {{ t('itemNum') }}: {{ orderFac.totalQuantity(order) }}
                </span>
                <span>AU$ {{ orderFac.grandTotal(order) }}</span>
              </div>
            </div>

            <!-- order items -->
            <q-scroll-area
              visible
              :thumb-style="scrollbarStyle"
              class="col-grow"
            >
              <q-list>
                <q-slide-item
                  v-for="item in cart"
                  :key="item.product.uuid"
                  class="q-py-sm"
                  @click="showItemDialog(item)"
                >
                  <!-- item info -->
                  <q-item>
                    <q-item-section>
                      <q-item-label class="text-h6 text-bold">
                        {{ item.product.name }}
                      </q-item-label>
                    </q-item-section>
                  </q-item>
                  <q-item>
                    <q-item-section class="text-subtitle1">
                      AU$ {{ formatNumber(item.price, 2) }}
                    </q-item-section>
                    <q-item-section side class="text-h6 text-bold">
                      <q-item-label>
                        AU$ {{ orderFac.getItemTotal(item) }}
                      </q-item-label>
                    </q-item-section>
                  </q-item>
                  <!-- is free -->
                  <div
                    class="text-subtitle2 text-negative text-bold q-px-md"
                    v-if="item.is_free"
                  >
                    {{ t('orderItem.is_free') }}
                  </div>

                  <!-- quantity input -->
                  <ItemQuantityInput
                    :item="item"
                    @remove-item="removeFromCart"
                    @update-quantity="updateItemQuantity"
                  />
                </q-slide-item>
              </q-list>
            </q-scroll-area>

            <ItemEditDialog
              v-model="itemDialog"
              :item="selectedItem"
              @remove-item="removeFromCart"
            />
          </div>
        </div>
      </div>
    </q-card>
    <!-- right side (products / checkout) -->
    <q-card square class="col-9">
      <!-- 1. select product -->
      <div class="column full-height" v-if="!isCheckoutStep">
        <!-- category -->
        <q-scroll-area
          visible
          :content-style="categoryContentStyle"
          :content-active-style="categoryContentStyle"
          :thumb-style="scrollbarStyle"
          class="category-content col-2 col-md-1 q-py-none"
        >
          <div class="row items-center no-wrap">
            <q-btn
              v-for="category in categories"
              :key="category.id"
              type="button"
              :label="category.name"
              @click="showProducts(category)"
              no-caps
              no-wrap
              flat
              unelevated
              :ripple="false"
              size="lg"
              class="q-py-none"
              :class="{ active: currentCategoryID === category.id }"
            />

            <!-- <q-btn-toggle
              v-model="currentCategoryID"
              :options="categories"
              stretch
              square
              unelevated
              no-caps
              no-wrap
              size="lg"
            /> -->
          </div>
        </q-scroll-area>

        <!-- product -->
        <q-scroll-area visible class="col-8 col-md-10 q-pa-lg bg-cream">
          <div class="row q-col-gutter-md">
            <div
              v-for="product in productItems"
              :key="product.uuid"
              class="col-12 col-sm-4 col-md-3"
            >
              <q-card flat bordered class="product-card">
                <q-btn
                  type="button"
                  @click="handleProductClick(product)"
                  flat
                  class="fit q-pa-none"
                >
                  <q-img
                    :src="`/api/${product?.images[0]?.image_path}`"
                    :alt="product.name"
                    :ratio="1"
                    fit="fill"
                    class="product-image full-width"
                    v-if="product.images.length > 0"
                  />

                  <q-card-section class="product-content">
                    <div class="product text-subtitle1">
                      <div class="product-title">{{ product.name }}</div>
                      <div class="product-price">
                        <template v-if="product.is_flexible_price">
                          <div class="text-orange text-weight-bold">
                            {{ t('flexiblePrice') }}
                          </div>
                        </template>
                        <template v-else-if="
                            product.sale_price > 0 &&
                            product.sale_price < product.price
                          ">
                          <div
                            class="original-price text-strike text-grey-6 text-subtitle2"
                          >
                            AU$ {{ formatNumber(product.price, 2) }}
                          </div>
                          <!-- 特價 -->
                          <div class="sale-price text-red text-weight-bold">
                            AU$ {{ formatNumber(product.sale_price, 2) }}
                          </div>
                        </template>
                        <template v-else>
                          AU$ {{ formatNumber(product.price, 2) }}
                        </template>
                      </div>
                    </div>
                  </q-card-section>
                </q-btn>
              </q-card>
            </div>
          </div>
        </q-scroll-area>

        <!-- actions -->
        <div class="col-2 col-md-1 footer-action">
          <div class="row fit">
            <div class="col-grow self-center">
              <div class="barcode-scanner-status q-ma-md">
                <BarcodeScannerWrapper
                  :products="products"
                  barcodeField="barcode"
                  @product-found="addToCart"
                />
              </div>
            </div>
            <div class="col-2">
              <q-btn
                type="button"
                @click="goNext"
                flat
                icon="arrow_forward_ios"
                size="lg"
                class="fit"
              />
            </div>
          </div>
        </div>
      </div>
      <!-- 2. checkout (discount, pay type, summary) -->
      <div class="row full-height" v-else>
        <!-- discount / pay type -->
        <q-card flat square bordered class="col-6">
          <DiscountCalculator
            v-model:rebate="order.rebate"
            v-model:discount="order.discount"
          />

          <!-- pay type -->
          <q-card-section>
            <h6>{{ t('payType') }}</h6>
            <q-btn
              v-for="type in orderFac.payTypes"
              :key="type.value"
              type="button"
              @click="order.pay_type = type.value"
              no-caps
              unelevated
              :ripple="false"
              :color="order.pay_type === type.value ? 'positive' : 'primary'"
              class="full-width row q-mb-md"
            >
              <q-icon :name="type.icon" class="col-2" />
              <span class="col-grow">{{ t(type.label) }}</span>
            </q-btn>
          </q-card-section>
        </q-card>
        <!-- total / checkout -->
        <q-card flat square bordered class="col-6">
          <div class="column full-height">
            <!-- total -->
            <div class="col-11">
              <q-card-section>
                <!-- Subtotal -->
                <div class="row items-center q-px-md">
                  <span class="col-5">
                    {{ t('subtotal') }}
                  </span>
                  <span class="col-grow text-h5 text-bold q-pl-sm">
                    AU$ {{ orderFac.getSubtotal(order) }}
                  </span>
                </div>
                <!-- Rebate -->
                <div
                  class="row items-center q-px-md"
                  v-if="
                    orderFac.getRebatePrice(
                      orderFac.getSubtotal(order),
                      order.rebate
                    ) > 0
                  "
                >
                  <span class="col-5">
                    {{ t('rebate') }}
                  </span>
                  <span class="col text-h5 text-bold">
                    -AU$
                    {{
                      orderFac.getRebatePrice(
                        orderFac.getSubtotal(order),
                        order.rebate
                      )
                    }}
                  </span>
                </div>
                <!-- Discount -->
                <div
                  class="row items-center q-px-md"
                  v-if="
                    orderFac.getPercentageOff(
                      orderFac.getSubtotal(order),
                      order.rebate,
                      order.discount
                    ) > 0
                  "
                >
                  <span class="col-5">
                    {{ discountText }}
                  </span>
                  <span class="col text-h5 text-bold">
                    -AU$
                    {{
                      orderFac.getPercentageOff(
                        orderFac.getSubtotal(order),
                        order.rebate,
                        order.discount
                      )
                    }}
                  </span>
                </div>

                <q-separator class="q-my-md" />

                <!-- Total -->
                <div class="row items-center q-px-md">
                  <span class="col-5">
                    {{ t('total') }}
                  </span>
                  <span class="col text-h5 text-bold q-pl-sm">
                    AU$ {{ orderFac.grandTotal(order) }}
                  </span>
                </div>
              </q-card-section>

              <!-- Note -->
              <q-card-section>
                <div class="row q-px-md q-mb-sm">
                  <span class="col-12">
                    {{ t('note.label') }}
                  </span>
                </div>
                <div class="row q-px-md">
                  <span class="col-grow">
                    <q-input
                      v-model="order.notes"
                      type="textarea"
                      outlined
                      dense
                      hide-bottom-space
                      lazy-rules
                    />
                  </span>
                </div>
              </q-card-section>
            </div>
            <!-- checkout -->
            <div class="col-1">
              <q-btn
                type="button"
                @click="checkout"
                :label="t('checkout')"
                square
                no-caps
                icon="shopping_cart_checkout"
                color="positive"
                size="lg"
                class="fit"
                :loading="isCheckout"
              />
            </div>
          </div>
        </q-card>
      </div>
    </q-card>
  </q-page>

  <!-- Checkout Success Dialog -->
  <q-dialog v-model="showCheckoutSuccessDialog" persistent>
    <q-card style="min-width: 400px">
      <q-card-section class="row items-center">
        <q-icon name="check_circle" color="positive" size="2em" class="q-mr-md" />
        <div class="text-h6">{{ t('checkoutSuccess') }}</div>
      </q-card-section>

      <q-card-section>
        <div class="text-body1">
          {{ t('checkoutSuccessMessage') }}
        </div>
        <div class="text-caption text-grey q-mt-sm">
          {{ t('orderNumber') }}: {{ order.order_no }}
        </div>
      </q-card-section>

      <q-card-actions align="between" class="q-pa-md">
        <q-btn
          flat
          :label="t('close')"
          color="grey"
          @click="closeSuccessDialog"
        />

        <div class="row q-gutter-sm">
          <q-btn
            :label="t('printInvoice')"
            color="primary"
            icon="print"
            @click="handlePrintInvoice"
          />

          <q-btn
            :label="t('sendEmail')"
            color="secondary"
            icon="email"
            @click="handleSendEmail"
            v-if="canSendEmail"
          />
        </div>
      </q-card-actions>
    </q-card>
  </q-dialog>

  <!-- 彈性金額輸入對話框 -->
  <q-dialog v-model="flexiblePriceDialog" persistent>
    <q-card style="min-width: 350px">
      <q-card-section>
        <div class="text-h6">{{ t('setServiceAmount') }}</div>
        <div class="text-subtitle2 text-grey-7 q-mt-sm">
          {{ selectedFlexibleProduct?.name }}
        </div>
      </q-card-section>

      <q-card-section class="q-pt-none">
        <q-input
          v-model.number="flexiblePrice"
          type="number"
          min="0"
          step="0.01"
          :label="t('enterAmountLabel')"
          outlined
          autofocus
          @keyup.enter="confirmFlexiblePrice"
        />
      </q-card-section>

      <q-card-actions align="right">
        <q-btn
          flat
          :label="t('cancel')"
          text-color="negative"
          @click="cancelFlexiblePrice"
        />
        <q-btn
          flat
          :label="t('confirm')"
          text-color="positive"
          @click="confirmFlexiblePrice"
          :disable="!flexiblePrice || flexiblePrice <= 0"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script lang="ts" setup>
import { ref, watch, computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { ProductCategoryApi, ProductCategory } from '@/api/productCategory';
import { ProductApi, Product } from '@/api/product';
import { OrderApi, Order, OrderItem } from '@/api/order';
import { useOrder } from '@/composables/useOrder';
import { Customer } from '@/api/customer';
import CustomerDialog from '@/components/CustomerDialog.vue';
import DiscountCalculator from '@/components/DiscountCalculator.vue';
import BarcodeScannerWrapper from '@/components/BarcodeScannerWrapper.vue';
import ItemEditDialog from './components/ItemEditDialog.vue';
import { formatDate, formatNumber, useDialog } from '@/utils';
import { usePrintInvoice } from '@/composables/usePrintInvoice';
import { XeroApi } from '@/api/xero';

const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const $q = useQuasar();

const orderFac = useOrder();
const dialog = useDialog();
const { printInvoice } = usePrintInvoice();

const order = ref<Order>(orderFac.newOrder(null));
const cart = ref<OrderItem[]>([]);

const categories = ref<ProductCategory[]>([]);
const currentCategoryID = ref(0);
const categoryContentStyle = {
  backgroundColor: 'rgba(221, 221, 221, 0.3)',
};
const scrollbarStyle = {
  height: '2px',
  width: '2px',
};

const discountText = computed(() =>
  t('percentageOff', { discount: order.value.discount })
);

const products = ref<Product[]>([]);
const productItems = computed(() =>
  products.value.filter(
    (product) => product.category_id === currentCategoryID.value
  )
);

// Date picker related variables
const datePickerDialog = ref(false);
const selectedDate = ref('');
const selectedTime = ref('');

// 彈性金額相關變數
const flexiblePriceDialog = ref(false);
const selectedFlexibleProduct = ref<Product | null>(null);
const flexiblePrice = ref(0);

const showDatePickerDialog = () => {
  datePickerDialog.value = true;
  selectedDate.value = formatDate(order.value.order_at, 'YYYY/MM/DD');
  selectedTime.value = formatDate(order.value.order_at, 'HH:mm');
};

// Update order date and time function
const updateOrderDate = (value: string) => {
  const time = formatDate(order.value.order_at, 'HH:mm');

  order.value.order_at = `${value} ${time}`;
};

const updateOrderTime = (value: string | null) => {
  const date = formatDate(order.value.order_at, 'YYYY/MM/DD');

  order.value.order_at = `${date} ${value}`;
};

const itemDialog = ref(false);
const selectedItem = ref<OrderItem>({
  uuid: '',
  product: {
    uuid: '',
    name: '',
  },
  quantity: 1,
  price: 0,
  regular_price: 0,
  rebate: 0,
  discount: 0,
  is_free: false,
  total: 0,
});
const showItemDialog = (item: OrderItem) => {
  selectedItem.value = item;
  itemDialog.value = true;
};

const getOrderIDFromRoute = (): string => {
  return route.params.orderID as string;
};

const showProducts = (category: ProductCategory) => {
  currentCategoryID.value = category.id;
};

// 處理產品點擊
const handleProductClick = (product: Product) => {
  if (product.is_flexible_price) {
    // 彈性金額產品，顯示金額輸入對話框
    selectedFlexibleProduct.value = product;
    flexiblePrice.value = 0;
    flexiblePriceDialog.value = true;
  } else {
    // 固定金額產品，直接加入購物車
    addToCart(product);
  }
};

// 確認彈性金額
const confirmFlexiblePrice = () => {
  if (selectedFlexibleProduct.value && flexiblePrice.value > 0) {
    addToCartWithCustomPrice(selectedFlexibleProduct.value, flexiblePrice.value);
    flexiblePriceDialog.value = false;
    selectedFlexibleProduct.value = null;
    flexiblePrice.value = 0;
  }
};

// 取消彈性金額輸入
const cancelFlexiblePrice = () => {
  flexiblePriceDialog.value = false;
  selectedFlexibleProduct.value = null;
  flexiblePrice.value = 0;
};

// 使用自定義價格加入購物車
const addToCartWithCustomPrice = (product: Product, customPrice: number) => {
  // 彈性金額產品每次都作為新項目添加，因為價格可能不同
  // 生成唯一的UUID來區分不同價格的同一產品
  const uniqueUuid = `${product.uuid}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  cart.value.push({
    uuid: uniqueUuid,
    product: {
      uuid: product.uuid,
      name: `${product.name} (AU$ ${customPrice.toFixed(2)})`,
    },
    quantity: 1,
    price: customPrice,
    regular_price: customPrice,
    rebate: 0,
    discount: 0,
    is_free: false,
    total: customPrice * 1, // 確保總價計算正確
  });
};

const addToCart = (product: Product) => {
  const index = cart.value.findIndex(
    (item) => item.product.uuid === product.uuid
  );
  if (index === -1) {
    // 如果商品不在購物車中，新增一個新的項目
    const price = product.sale_price > 0 ? product.sale_price : product.price;

    cart.value.push({
      uuid: '',
      product: {
        uuid: product.uuid,
        name: product.name,
      },
      quantity: 1,
      price: price,
      regular_price: product.price,
      rebate: 0,
      discount: 0,
      is_free: false,
      total: price * 1, // 使用實際價格計算總價
    });
  } else {
    // 固定價格產品，增加數量
    let item = cart.value[index];
    item.quantity += 1;
    item.total = item.price * item.quantity;
  }
};

const removeFromCart = (item: OrderItem) => {
  const index = cart.value.findIndex(
    (cartItem) => {
      // 如果項目有唯一的UUID（彈性金額產品），使用UUID匹配
      if (item.uuid && cartItem.uuid) {
        return cartItem.uuid === item.uuid;
      }
      // 否則使用產品UUID匹配（固定價格產品）
      return cartItem.product.uuid === item.product.uuid;
    }
  );
  if (index !== -1) {
    cart.value.splice(index, 1);
  }
};

const updateItemQuantity = (item: OrderItem, newQuantity: number) => {
  const index = cart.value.findIndex(
    (cartItem) => {
      // 如果項目有唯一的UUID（彈性金額產品），使用UUID匹配
      if (item.uuid && cartItem.uuid) {
        return cartItem.uuid === item.uuid;
      }
      // 否則使用產品UUID匹配（固定價格產品）
      return cartItem.product.uuid === item.product.uuid;
    }
  );
  if (index !== -1) {
    cart.value[index].quantity = newQuantity;
    cart.value[index].total = cart.value[index].price * newQuantity;
  }
};

const customerDialog = ref(false);
const showCustomerDialog = () => {
  customerDialog.value = true;
};

const isCheckoutStep = ref(false);
const goBack = () => {
  if (isCheckoutStep.value) {
    isCheckoutStep.value = false;
  } else {
    router.push('/order');
  }
};
const goNext = () => {
  isCheckoutStep.value = true;
};

const isCheckout = ref(false);
const showCheckoutSuccessDialog = ref(false);

// 計算屬性：檢查是否可以發送 Email
const canSendEmail = computed(() => {
  return !!(
    order.value?.customer?.uuid &&
    order.value?.customer?.email &&
    order.value.xero_sync?.xero_invoice_id &&
    order.value.xero_sync.sync_status === 'success'
  );
});
const checkout = async () => {
  if (!checkoutValid()) {
    dialog.showMessage({
      message: '',
      title: t('checkoutError.noItems'),
      color: 'negative',
      timeout: 0,
      persistent: true,
    });
    return;
  }

  try {
    isCheckout.value = true;

    orderFac.checkout(order.value);
    await OrderApi.checkout(order.value);

    // 延遲 2 秒以確保 Xero 同步完成
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // 取得最新的訂單資料
    const response = await OrderApi.get(order.value.uuid);
    const result = response.result;

    order.value.customer = result.customer;
    order.value.customer_uuid = order.value.customer?.uuid;
    order.value.customer_name = order.value.customer?.name;
    order.value.xero_sync = result.xero_sync;

    // 顯示成功對話框
    showCheckoutSuccessDialog.value = true;
  } finally {
    isCheckout.value = false;
  }
};

const checkoutValid = () => {
  if (cart.value.length === 0) {
    return false;
  }
  return true;
};

const currentCustomer = ref<Customer | null>(null);
const onSelectCustomer = (customer: Customer) => {
  currentCustomer.value = customer;
  order.value.customer_uuid = customer.uuid;
  order.value.customer_name = customer.name;
};

// 對話框處理方法
const closeSuccessDialog = () => {
  showCheckoutSuccessDialog.value = false;
  router.push('/order');
};

const handlePrintInvoice = async () => {
  try {
    await printInvoice(order.value);
    $q.notify({
      type: 'positive',
      message: t('printInvoiceSuccess'),
      position: 'top',
    });
  } catch (error) {
    console.error('Print invoice error:', error);
    $q.notify({
      type: 'negative',
      message: t('printInvoiceError'),
      position: 'top',
    });
  }
};

const handleSendEmail = async () => {
  // 檢查是否有客戶且客戶有 email
  if (!order.value?.customer?.uuid || !order.value?.customer?.email) {
    $q.notify({
      type: 'warning',
      message: t('sendEmailNoCustomerOrEmail'),
      position: 'top',
    });
    return;
  }

  // 檢查是否已同步到 Xero
  if (!order.value.xero_sync?.xero_invoice_id || order.value.xero_sync.sync_status !== 'success') {
    $q.notify({
      type: 'warning',
      message: t('sendEmailNotSynced'),
      position: 'top',
    });
    return;
  }

  try {
    // 調用 Xero API 發送 email
    await XeroApi.sendInvoiceEmail(order.value.xero_sync.xero_invoice_id, order.value?.customer?.email);

    $q.notify({
      type: 'positive',
      message: t('sendEmailSuccess'),
      position: 'top',
    });
  } catch (error) {
    console.error('Send email error:', error);

    // 提供更詳細的錯誤處理
    let errorMessage = t('sendEmailError');

    if (error instanceof Error) {
      if (error.message.includes('daily email limit') || error.message.includes('Daily Email Rate Limit')) {
        errorMessage = t('sendEmailRateLimitError');

        // 顯示解決方案對話框
        $q.dialog({
          title: t('sendEmailRateLimitTitle'),
          message: t('sendEmailRateLimitMessage'),
          ok: {
            label: t('understood'),
            color: 'primary',
          },
          persistent: false,
        });

        return; // 不顯示一般的錯誤通知
      } else if (error.message.includes('invalid email') || error.message.includes('Invalid email')) {
        errorMessage = t('sendEmailInvalidEmailError');
      } else if (error.message.includes('manually from Xero')) {
        errorMessage = t('sendEmailManualError');
      }
    }

    $q.notify({
      type: 'negative',
      message: errorMessage,
      position: 'top',
      timeout: 5000, // 顯示更長時間讓用戶看到完整訊息
    });
  }
};

onMounted(() => {
  const orderID = getOrderIDFromRoute();
  // 載入訂單資料
  Promise.all([
    ProductCategoryApi.listCategories(),
    ProductApi.listProducts({}),
    OrderApi.get(orderID),
  ]).then(([categoryRes, productRes, orderRes]) => {
    categories.value = categoryRes.result;
    products.value = productRes.result.data;
    order.value = orderRes.result;

    cart.value = order.value.order_items;
    if (categories.value.length > 0) {
      showProducts(categories.value[0]);
    }
  });
});

// Initialize selected date and time when order is loaded
watch(
  () => order.value,
  (newOrder) => {
    if (newOrder && newOrder.order_at) {
      const dateObj = new Date(newOrder.order_at);
      selectedDate.value = formatDate(dateObj, 'YYYY/MM/DD');
      selectedTime.value = formatDate(dateObj, 'HH:mm');
    }
  },
  { immediate: true }
);
</script>

<style lang="scss">
.order-info {
  background-color: #ffddae;
  color: #131010;
}

.category-content {
  .q-btn.active {
    color: #eb8317;
  }
}

.product-card {
  width: 100%;
  aspect-ratio: 1;
  display: flex;
  position: relative;
  flex-direction: column;

  .q-btn__content {
    align-items: end;
  }
}

.product-image {
  width: 100%;
  position: absolute;
  left: 0;
  top: 0;
  object-fit: cover;
}

.product-content {
  display: flex;
  flex-grow: 1;
  flex-direction: column;
  background-color: rgba(255, 255, 255, 0.9);
  color: #131010;
}

.product-title {
  display: -webkit-box;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.5;
}

.footer-action {
  background-color: #ffddae;
}

.barcode-scanner-status {
  z-index: 9999;
  opacity: 0.8;
  transition: opacity 0.3s ease;

  &:hover {
    opacity: 1;
  }
}
</style>
