{"name": "cx-pos", "version": "0.3.1", "description": "A CX POS Web App", "productName": "CX POS", "author": "<PERSON><PERSON> <<EMAIL>>", "private": true, "scripts": {"lint": "eslint --ext .js,.ts,.vue ./", "format": "prettier --write \"**/*.{js,ts,vue,scss,html,md,json}\" --ignore-path .gitignore", "test": "echo \"No test specified\" && exit 0", "dev": "quasar dev -m pwa", "build": "node scripts/generate-version.js && quasar build -m pwa"}, "dependencies": {"@quasar/extras": "^1.16.4", "@zxing/library": "^0.21.3", "axios": "^1.2.1", "dotenv": "^16.4.7", "html5-qrcode": "^2.3.8", "i18n-iso-countries": "^7.13.0", "mkcert": "^3.2.0", "pinia": "^2.0.11", "pinia-plugin-persistedstate": "^4.2.0", "quasar": "^2.16.0", "vue": "^3.4.18", "vue-i18n": "^9.2.2", "vue-router": "^4.0.12", "vuedraggable": "^4.1.0"}, "devDependencies": {"@intlify/vite-plugin-vue-i18n": "^3.3.1", "@quasar/app-vite": "^1.9.0", "@types/node": "^12.20.21", "@typescript-eslint/eslint-plugin": "^5.10.0", "@typescript-eslint/parser": "^5.10.0", "autoprefixer": "^10.4.2", "eslint": "^8.57.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-vue": "^9.0.0", "prettier": "^2.5.1", "typescript": "^5.5.4", "vite-plugin-checker": "^0.6.4", "vue-tsc": "^2.0.29", "workbox-build": "^7.1.1", "workbox-cacheable-response": "^7.1.0", "workbox-core": "^7.1.0", "workbox-expiration": "^7.1.0", "workbox-precaching": "^7.1.0", "workbox-routing": "^7.1.0", "workbox-strategies": "^7.1.0"}, "engines": {"node": "^20", "npm": ">= 10.7.0", "yarn": ">= 1.21.1"}}