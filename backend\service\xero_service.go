package service

import (
	"context"
	"crypto/rand"
	"cx/domain"
	"cx/repository"
	"cx/utils"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"gorm.io/gorm"
)

type XeroService interface {
	// Config
	SaveConfig(ctx context.Context, req *domain.XeroAuthRequest) (*domain.XeroConfig, error)
	GetConfig(ctx context.Context) (*domain.XeroConfig, error)

	// Auth
	GenerateAuthURL(ctx context.Context) (string, string, error)
	HandleCallback(ctx context.Context, code string) (*domain.XeroToken, error)
	RefreshToken(ctx context.Context) (*domain.XeroToken, error)
	GetValidToken(ctx context.Context) (*domain.XeroToken, error)
	ValidateConnection(ctx context.Context) error

	Disconnect(ctx context.Context) error

	// Invoice
	GetInvoices(ctx context.Context, params *domain.XeroInvoiceParams) (*domain.XeroInvoicesResponse, error)
	GetInvoice(ctx context.Context, invoiceID string) (*domain.XeroInvoiceResponse, error)
	GetInvoicePDF(ctx context.Context, invoiceID string) ([]byte, error)
	SendInvoiceEmail(ctx context.Context, invoiceID string, email string) error
	CheckEmailLimitStatus(ctx context.Context) (bool, error)
	CreateInvoice(ctx context.Context, invoice *domain.XeroInvoiceRequest) (*domain.XeroInvoiceResponse, error)
	UpdateInvoice(ctx context.Context, invoiceID string, invoice *domain.XeroInvoiceRequest) (*domain.XeroInvoiceResponse, error)
	VoidInvoice(ctx context.Context, invoiceID string) (*domain.XeroInvoiceResponse, error)

	// Accounts
	GetAccounts(ctx context.Context) (*domain.XeroAccountsResponse, error)
	GetCashAccount(ctx context.Context) (*domain.XeroAccount, error)

	// Payment
	CreatePayment(ctx context.Context, payment *domain.XeroPaymentRequest) (*domain.XeroPaymentResponse, error)

	// Order Sync
	SyncOrderToXero(ctx context.Context, orderUUID string) (*domain.XeroInvoiceResponse, error)
	VoidOrderInXero(ctx context.Context, orderUUID string) error
}

type xeroService struct {
	db *gorm.DB
}

func NewXeroService(db *gorm.DB) XeroService {
	return &xeroService{db}
}

// SaveConfig 儲存Xero設定
func (s *xeroService) SaveConfig(ctx context.Context, req *domain.XeroAuthRequest) (*domain.XeroConfig, error) {
	config := &domain.XeroConfig{}

	err := s.db.Transaction(func(tx *gorm.DB) error {
		xeroRepo := repository.NewXeroRepository(tx)

		// 先檢查是否已存在設定
		existingConfig, err := xeroRepo.GetConfig(ctx)
		if err == nil {
			// 更新現有設定
			existingConfig.ClientID = req.ClientID
			existingConfig.ClientSecret = req.ClientSecret
			existingConfig.RedirectURI = req.RedirectURI
			existingConfig.Scopes = req.Scopes

			err := s.db.Save(&existingConfig).Error
			config = existingConfig
			return err
		} else if err == gorm.ErrRecordNotFound {
			// 建立新設定
			newConfig := &domain.XeroConfig{
				UUID:         utils.GenerateUUID(),
				ClientID:     req.ClientID,
				ClientSecret: req.ClientSecret,
				RedirectURI:  req.RedirectURI,
				Scopes:       req.Scopes,
				IsActive:     true,
			}

			err := s.db.Create(newConfig).Error
			config = newConfig
			return err
		}

		return err
	})

	return config, err
}

// GetConfig 取得Xero設定
func (s *xeroService) GetConfig(ctx context.Context) (*domain.XeroConfig, error) {
	xeroRepo := repository.NewXeroRepository(s.db)

	return xeroRepo.GetConfig(ctx)
}

// GenerateAuthURL 產生OAuth2認證URL
func (s *xeroService) GenerateAuthURL(ctx context.Context) (string, string, error) {
	config, err := s.GetConfig(ctx)
	if err != nil {
		return "", "", err
	}

	// 產生state參數
	state, err := s.generateState()
	if err != nil {
		return "", "", err
	}

	// 建構OAuth2 URL
	baseURL := "https://login.xero.com/identity/connect/authorize"
	params := url.Values{}
	params.Add("response_type", "code")
	params.Add("client_id", config.ClientID)
	params.Add("redirect_uri", config.RedirectURI)
	params.Add("scope", config.Scopes)
	params.Add("state", state)

	authURL := fmt.Sprintf("%s?%s", baseURL, params.Encode())

	return authURL, state, nil
}

// HandleCallback 處理OAuth2回調
func (s *xeroService) HandleCallback(ctx context.Context, code string) (*domain.XeroToken, error) {
	config, err := s.GetConfig(ctx)
	if err != nil {
		return nil, err
	}

	// 交換授權碼取得token
	tokenResp, err := s.exchangeCodeForToken(config, code)
	if err != nil {
		return nil, err
	}

	// 除錯：檢查 token 回應
	fmt.Printf("Token response: AccessToken=%s, TokenType=%s, ExpiresIn=%d\n",
		tokenResp.AccessToken, tokenResp.TokenType, tokenResp.ExpiresIn)

	// 取得連接資訊
	connections, err := s.getConnections(tokenResp.AccessToken)
	if err != nil {
		return nil, fmt.Errorf("failed to get connections: %v", err)
	}

	// 除錯：列印連接資訊
	fmt.Printf("Found %d connections\n", len(connections))
	for i, conn := range connections {
		fmt.Printf("Connection %d: ID=%s, TenantID=%s, TenantName=%s\n",
			i, conn.ID, conn.TenantID, conn.TenantName)
	}

	// 儲存token（假設取第一個組織）
	var tenantID, tenantName string
	if len(connections) > 0 {
		tenantID = connections[0].TenantID
		tenantName = connections[0].TenantName
		fmt.Printf("Selected tenant: ID=%s, Name=%s\n", tenantID, tenantName)
	}

	expiresAt := time.Now().Add(time.Duration(tokenResp.ExpiresIn) * time.Second)
	token := &domain.XeroToken{
		UUID:         utils.GenerateUUID(),
		ConfigID:     config.ID,
		AccessToken:  tokenResp.AccessToken,
		RefreshToken: tokenResp.RefreshToken,
		TokenType:    tokenResp.TokenType,
		ExpiresAt:    &expiresAt,
		TenantID:     tenantID,
		TenantName:   tenantName,
	}

	err = s.db.Transaction(func(tx *gorm.DB) error {
		xeroRepo := repository.NewXeroRepository(tx)

		// 刪除舊的token
		if err := xeroRepo.DeleteToken(ctx); err != nil {
			return err
		}

		// 儲存新的token
		if err := xeroRepo.CreateToken(ctx, token); err != nil {
			return err
		}

		return nil
	})

	return token, err
}

// RefreshToken 刷新Token
func (s *xeroService) RefreshToken(ctx context.Context) (*domain.XeroToken, error) {
	var updatedToken *domain.XeroToken

	err := s.db.Transaction(func(tx *gorm.DB) error {
		xeroRepo := repository.NewXeroRepository(tx)

		existingToken, err := xeroRepo.GetToken(ctx)
		if err != nil {
			fmt.Printf("Failed to get existing token during refresh: %v\n", err)
			return err
		}

		fmt.Printf("Refreshing token: %s...\n", existingToken.AccessToken[:20])

		tokenResp, err := s.refreshAccessToken(existingToken)
		if err != nil {
			fmt.Printf("Failed to refresh access token: %v\n", err)
			return err
		}

		// 創建更新的token，保留所有原有信息
		updatedToken = &domain.XeroToken{
			ID:           existingToken.ID,
			UUID:         existingToken.UUID,
			ConfigID:     existingToken.ConfigID,
			TenantID:     existingToken.TenantID,
			TenantName:   existingToken.TenantName,
			AccessToken:  tokenResp.AccessToken,
			RefreshToken: tokenResp.RefreshToken,
			TokenType:    tokenResp.TokenType,
			ExpiresAt:    &[]time.Time{time.Now().Add(time.Duration(tokenResp.ExpiresIn) * time.Second)}[0],
		}

		fmt.Printf("Token refresh completed - TenantID: %s, TenantName: %s\n", updatedToken.TenantID, updatedToken.TenantName)
		fmt.Printf("New access token: %s...\n", updatedToken.AccessToken[:20])

		err = xeroRepo.UpdateToken(ctx, updatedToken)
		if err != nil {
			fmt.Printf("Failed to update token in database: %v\n", err)
			return err
		}

		fmt.Printf("Token successfully updated in database\n")
		return nil
	})

	if err != nil {
		return nil, err
	}

	return updatedToken, nil
}

// GetValidToken 取得有效的Token
func (s *xeroService) GetValidToken(ctx context.Context) (*domain.XeroToken, error) {
	xeroRepo := repository.NewXeroRepository(s.db)

	existingToken, err := xeroRepo.GetToken(ctx)
	if err != nil {
		fmt.Printf("Failed to get token from database: %v\n", err)
		return nil, err
	}

	fmt.Printf("Retrieved token from DB: %s... (expires: %v)\n",
		existingToken.AccessToken[:20], existingToken.ExpiresAt)

	// 檢查token是否過期
	if existingToken.ExpiresAt != nil && time.Now().After(*existingToken.ExpiresAt) {
		fmt.Printf("Token is expired, refreshing...\n")
		return s.RefreshToken(ctx)
	}

	fmt.Printf("Token is valid, returning existing token\n")
	return existingToken, nil
}

// ValidateConnection 驗證 Xero 連接是否有效
func (s *xeroService) ValidateConnection(ctx context.Context) error {
	// 嘗試獲取組織信息來驗證連接
	resp, err := s.makeXeroAPIRequest(ctx, "GET", "/Organisation", nil)
	if err != nil {
		return fmt.Errorf("failed to validate connection: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read validation response: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		if resp.StatusCode == 403 {
			return fmt.Errorf("Xero connection is invalid or has been revoked. Please reconnect your Xero account")
		}
		return fmt.Errorf("connection validation failed: %d - %s", resp.StatusCode, string(body))
	}

	fmt.Printf("Xero connection validated successfully\n")
	return nil
}

func (s *xeroService) Disconnect(ctx context.Context) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		xeroRepo := repository.NewXeroRepository(tx)

		// 刪除token
		if err := xeroRepo.DeleteToken(ctx); err != nil {
			return err
		}

		// 刪除連接
		// if err := xeroRepo.DeleteConnection(ctx); err != nil {
		// 	return err
		// }

		return nil
	})
}

func (s *xeroService) generateState() (string, error) {
	b := make([]byte, 32)
	_, err := rand.Read(b)
	if err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(b), nil
}

func (s *xeroService) exchangeCodeForToken(config *domain.XeroConfig, code string) (*domain.XeroTokenResponse, error) {
	// 除錯：列印設定資訊
	fmt.Printf("Client ID: %s\n", config.ClientID)
	fmt.Printf("Client ID Length: %d\n", len(config.ClientID))
	fmt.Printf("Client Secret Length: %d\n", len(config.ClientSecret))
	fmt.Printf("Client Secret: %s\n", config.ClientSecret) // 臨時顯示完整 secret 用於除錯
	fmt.Printf("Redirect URI: %s\n", config.RedirectURI)
	fmt.Printf("Authorization Code: %s\n", code)

	data := url.Values{}
	data.Set("grant_type", "authorization_code")
	data.Set("client_id", config.ClientID)
	data.Set("code", code)
	data.Set("redirect_uri", config.RedirectURI)

	// 除錯：列印請求資料
	fmt.Printf("Request data: %s\n", data.Encode())

	req, err := http.NewRequest("POST", "https://identity.xero.com/connect/token", strings.NewReader(data.Encode()))
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// 使用手動 Basic Auth 編碼（確保正確性）
	auth := base64.StdEncoding.EncodeToString([]byte(config.ClientID + ":" + config.ClientSecret))
	req.Header.Set("Authorization", "Basic "+auth)

	// 除錯：列印請求標頭和認證資訊
	fmt.Printf("Basic Auth string: Basic %s\n", auth)

	// 驗證 Basic Auth 編碼
	decoded, err := base64.StdEncoding.DecodeString(auth)
	if err != nil {
		fmt.Printf("Failed to decode Basic Auth: %v\n", err)
	} else {
		fmt.Printf("Decoded Basic Auth: %s\n", string(decoded))
	}

	fmt.Printf("Request headers: %+v\n", req.Header)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 讀取回應內容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// 除錯：列印 token 交換回應
	fmt.Printf("Token exchange response status: %d\n", resp.StatusCode)
	fmt.Printf("Token exchange response body: %s\n", string(body))

	// 檢查 HTTP 狀態碼
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("token exchange failed with status %d: %s", resp.StatusCode, string(body))
	}

	var tokenResp domain.XeroTokenResponse
	err = json.Unmarshal(body, &tokenResp)
	if err != nil {
		return nil, fmt.Errorf("failed to parse token response: %v", err)
	}

	return &tokenResp, nil
}

func (s *xeroService) refreshAccessToken(token *domain.XeroToken) (*domain.XeroTokenResponse, error) {
	data := url.Values{}
	data.Set("grant_type", "refresh_token")
	data.Set("refresh_token", token.RefreshToken)

	req, err := http.NewRequest("POST", "https://identity.xero.com/connect/token", strings.NewReader(data.Encode()))
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.SetBasicAuth(token.Config.ClientID, token.Config.ClientSecret)

	fmt.Printf("Refreshing token for tenant: %s\n", token.TenantID)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 讀取回應內容以便調試
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	fmt.Printf("Token refresh response status: %d\n", resp.StatusCode)
	fmt.Printf("Token refresh response body: %s\n", string(body))

	// 檢查 HTTP 狀態碼
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("token refresh failed with status %d: %s", resp.StatusCode, string(body))
	}

	var tokenResp domain.XeroTokenResponse
	err = json.Unmarshal(body, &tokenResp)
	if err != nil {
		return nil, fmt.Errorf("failed to parse token refresh response: %v", err)
	}

	fmt.Printf("Token refreshed successfully, new access token: %s...\n", tokenResp.AccessToken[:20])

	return &tokenResp, nil
}

func (s *xeroService) getConnections(accessToken string) ([]domain.XeroConnection, error) {
	// 除錯：檢查 access token
	fmt.Printf("Using access token: %s\n", accessToken)

	req, err := http.NewRequest("GET", "https://api.xero.com/connections", nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Authorization", "Bearer "+accessToken)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")

	// 除錯：列印請求標頭
	fmt.Printf("Request headers: %+v\n", req.Header)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 除錯：檢查回應狀態
	fmt.Printf("Response status: %d %s\n", resp.StatusCode, resp.Status)

	// 讀取原始回應內容以便除錯
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// 記錄原始回應（用於除錯）
	fmt.Printf("Xero connections API response: %s\n", string(body))

	// 嘗試直接解析為陣列
	var connections []domain.XeroConnection
	err = json.Unmarshal(body, &connections)
	if err != nil {
		// 如果失敗，嘗試解析為包含 connections 欄位的物件
		var connectionResp domain.XeroConnectionResponse
		err = json.Unmarshal(body, &connectionResp)
		if err != nil {
			// 如果都失敗，嘗試解析為通用的 map 來查看實際結構
			var rawData interface{}
			if jsonErr := json.Unmarshal(body, &rawData); jsonErr == nil {
				fmt.Printf("Raw JSON structure: %+v\n", rawData)
			}
			return nil, fmt.Errorf("failed to parse connections response: %v, raw response: %s", err, string(body))
		}
		return connectionResp.Connections, nil
	}

	// 除錯：檢查解析後的連接資料
	fmt.Printf("Parsed %d connections from API\n", len(connections))
	for i, conn := range connections {
		fmt.Printf("Raw connection %d: %+v\n", i, conn)
	}

	return connections, nil
}

// makeXeroAPIRequest 統一的 Xero API 請求方法，包含自動 Token 刷新
func (s *xeroService) makeXeroAPIRequest(ctx context.Context, method, endpoint string, body io.Reader) (*http.Response, error) {
	// 讀取 body 內容以便重試時重用
	var bodyBytes []byte
	if body != nil {
		var err error
		bodyBytes, err = io.ReadAll(body)
		if err != nil {
			return nil, fmt.Errorf("failed to read request body: %v", err)
		}
	}

	return s.makeXeroAPIRequestWithRetry(ctx, method, endpoint, bodyBytes, false)
}

// makeXeroAPIRequestWithRetry 內部方法，支援重試
func (s *xeroService) makeXeroAPIRequestWithRetry(ctx context.Context, method, endpoint string, bodyBytes []byte, isRetry bool) (*http.Response, error) {
	// 每次都重新獲取 token，確保使用最新的 token
	token, err := s.GetValidToken(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get valid token: %v", err)
	}

	url := fmt.Sprintf("https://api.xero.com/api.xro/2.0%s", endpoint)

	var bodyReader io.Reader
	if bodyBytes != nil {
		bodyReader = strings.NewReader(string(bodyBytes))
	}

	req, err := http.NewRequest(method, url, bodyReader)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Authorization", "Bearer "+token.AccessToken)
	req.Header.Set("Xero-tenant-id", token.TenantID)
	req.Header.Set("Accept", "application/json")
	if method == "POST" || method == "PUT" {
		req.Header.Set("Content-Type", "application/json")
	}

	// 調試信息
	fmt.Printf("Making Xero API request: %s %s (retry: %v)\n", method, url, isRetry)
	fmt.Printf("Using tenant ID: %s\n", token.TenantID)
	fmt.Printf("Access token: %s...\n", token.AccessToken[:20])
	fmt.Printf("Token expires at: %v\n", token.ExpiresAt)

	// 檢查 TenantID 是否為空
	if token.TenantID == "" {
		return nil, fmt.Errorf("tenant ID is empty - this may indicate a token refresh issue")
	}

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}

	// 如果 Token 過期或認證失敗且尚未重試，嘗試刷新並重試
	if (resp.StatusCode == 401 || resp.StatusCode == 403) && !isRetry {
		// 讀取錯誤回應以便記錄
		body, _ := io.ReadAll(resp.Body)
		resp.Body.Close()

		// 記錄錯誤詳情
		fmt.Printf("Xero API authentication error: %d - %s\n", resp.StatusCode, string(body))
		fmt.Printf("Current token (before refresh): %s...\n", token.AccessToken[:20])

		// 刷新 Token
		newToken, err := s.RefreshToken(ctx)
		if err != nil {
			return nil, fmt.Errorf("failed to refresh token after %d error: %v", resp.StatusCode, err)
		}

		fmt.Printf("Token refreshed successfully\n")
		fmt.Printf("Old token: %s...\n", token.AccessToken[:20])
		fmt.Printf("New token: %s...\n", newToken.AccessToken[:20])

		// 驗證新 token 確實不同
		if newToken.AccessToken == token.AccessToken {
			return nil, fmt.Errorf("token refresh did not produce a new access token")
		}

		// 重試請求 - 這次會重新獲取刷新後的 token
		return s.makeXeroAPIRequestWithRetry(ctx, method, endpoint, bodyBytes, true)
	}

	return resp, nil
}

// GetInvoices 取得發票列表
func (s *xeroService) GetInvoices(ctx context.Context, params *domain.XeroInvoiceParams) (*domain.XeroInvoicesResponse, error) {
	endpoint := "/Invoices"

	// 建構查詢參數
	if params != nil {
		fmt.Printf("GetInvoices params: Status=%s, ContactID=%s, DateFrom=%s, DateTo=%s, Page=%d\n",
			params.Status, params.ContactID, params.DateFrom, params.DateTo, params.Page)

		query := url.Values{}
		var whereConditions []string

		// 構建 where 條件
		if params.Status != "" {
			whereConditions = append(whereConditions, fmt.Sprintf("Status=\"%s\"", params.Status))
		}
		if params.ContactID != "" {
			whereConditions = append(whereConditions, fmt.Sprintf("Contact.ContactID=Guid(\"%s\")", params.ContactID))
		}
		if params.DateFrom != "" {
			// 解析日期並轉換為 Xero API 格式
			if parsedDate, err := time.Parse("2006-01-02", params.DateFrom); err == nil {
				// 使用 DateTime(YYYY,MM,DD) 格式 - 注意月份和日期要用兩位數
				whereConditions = append(whereConditions,
					fmt.Sprintf("Date>=DateTime(%d,%02d,%02d)",
						parsedDate.Year(), int(parsedDate.Month()), parsedDate.Day()))
				fmt.Printf("DateFrom condition: Date>=DateTime(%d,%02d,%02d)\n",
					parsedDate.Year(), int(parsedDate.Month()), parsedDate.Day())
			} else {
				fmt.Printf("Invalid DateFrom format: %s, error: %v\n", params.DateFrom, err)
			}
		}
		if params.DateTo != "" {
			// 解析日期並轉換為 Xero API 格式
			if parsedDate, err := time.Parse("2006-01-02", params.DateTo); err == nil {
				// 使用 DateTime(YYYY,MM,DD) 格式 - 注意月份和日期要用兩位數
				whereConditions = append(whereConditions,
					fmt.Sprintf("Date<=DateTime(%d,%02d,%02d)",
						parsedDate.Year(), int(parsedDate.Month()), parsedDate.Day()))
				fmt.Printf("DateTo condition: Date<=DateTime(%d,%02d,%02d)\n",
					parsedDate.Year(), int(parsedDate.Month()), parsedDate.Day())
			} else {
				fmt.Printf("Invalid DateTo format: %s, error: %v\n", params.DateTo, err)
			}
		}

		// 將所有 where 條件用 AND 連接
		if len(whereConditions) > 0 {
			whereClause := strings.Join(whereConditions, " AND ")
			query.Add("where", whereClause)
			fmt.Printf("Xero API where clause: %s\n", whereClause)
		}

		// 添加分頁參數
		if params.Page > 0 {
			query.Add("page", fmt.Sprintf("%d", params.Page))
		}

		// 構建最終的 endpoint
		if len(query) > 0 {
			endpoint += "?" + query.Encode()
			fmt.Printf("Xero API endpoint: %s\n", endpoint)
		}
	}

	resp, err := s.makeXeroAPIRequest(ctx, "GET", endpoint, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode != http.StatusOK {
		// 提供更詳細的錯誤信息
		if resp.StatusCode == 403 {
			return nil, fmt.Errorf("Xero API access forbidden (403): %s. This may indicate: 1) Token has been revoked, 2) Insufficient permissions, 3) Tenant ID mismatch, or 4) App not connected to organization", string(body))
		}
		return nil, fmt.Errorf("Xero API error: %d - %s", resp.StatusCode, string(body))
	}

	var invoicesResp domain.XeroInvoicesResponse
	err = json.Unmarshal(body, &invoicesResp)
	if err != nil {
		return nil, fmt.Errorf("failed to parse invoices response: %v", err)
	}

	return &invoicesResp, nil
}

// GetInvoice 取得單一發票
func (s *xeroService) GetInvoice(ctx context.Context, invoiceID string) (*domain.XeroInvoiceResponse, error) {
	endpoint := fmt.Sprintf("/Invoices/%s", invoiceID)

	resp, err := s.makeXeroAPIRequest(ctx, "GET", endpoint, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode != http.StatusOK {
		// 提供更詳細的錯誤信息
		if resp.StatusCode == 403 {
			return nil, fmt.Errorf("Xero API access forbidden (403): %s. This may indicate: 1) Token has been revoked, 2) Insufficient permissions, 3) Tenant ID mismatch, or 4) App not connected to organization", string(body))
		}
		return nil, fmt.Errorf("Xero API error: %d - %s", resp.StatusCode, string(body))
	}

	var invoiceResp domain.XeroInvoiceResponse
	err = json.Unmarshal(body, &invoiceResp)
	if err != nil {
		return nil, fmt.Errorf("failed to parse invoice response: %v", err)
	}

	return &invoiceResp, nil
}

// CreateInvoice 建立發票
func (s *xeroService) CreateInvoice(ctx context.Context, invoice *domain.XeroInvoiceRequest) (*domain.XeroInvoiceResponse, error) {
	endpoint := "/Invoices"

	jsonData, err := json.Marshal(invoice)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal invoice: %v", err)
	}

	resp, err := s.makeXeroAPIRequest(ctx, "POST", endpoint, strings.NewReader(string(jsonData)))
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusCreated {
		return nil, fmt.Errorf("Xero API error: %d - %s", resp.StatusCode, string(body))
	}

	var invoiceResp domain.XeroInvoiceResponse
	err = json.Unmarshal(body, &invoiceResp)
	if err != nil {
		return nil, fmt.Errorf("failed to parse invoice response: %v", err)
	}

	return &invoiceResp, nil
}

// GetAccounts 取得科目表
func (s *xeroService) GetAccounts(ctx context.Context) (*domain.XeroAccountsResponse, error) {
	endpoint := "/Accounts"

	resp, err := s.makeXeroAPIRequest(ctx, "GET", endpoint, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("Xero API error: %d - %s", resp.StatusCode, string(body))
	}

	var accountsResp domain.XeroAccountsResponse
	err = json.Unmarshal(body, &accountsResp)
	if err != nil {
		return nil, fmt.Errorf("failed to parse accounts response: %v", err)
	}

	return &accountsResp, nil
}

// GetCashAccount 取得現金帳戶
func (s *xeroService) GetCashAccount(ctx context.Context) (*domain.XeroAccount, error) {
	accounts, err := s.GetAccounts(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get accounts: %v", err)
	}

	// 尋找現金帳戶 - 優先順序：
	// 1. 類型為 BANK 且可用於付款的帳戶
	// 2. 名稱包含 "Cash" 的帳戶
	// 3. 代碼為常見現金帳戶代碼的帳戶 (090, 100, 110, 1000, 1100)
	var cashAccount *domain.XeroAccount
	var bankAccount *domain.XeroAccount
	var namedCashAccount *domain.XeroAccount

	for _, account := range accounts.Accounts {
		// 優先選擇 BANK 類型且可用於付款的帳戶
		if account.Type == "BANK" && account.EnablePaymentsToAccount {
			if bankAccount == nil {
				bankAccount = &account
			}
		}

		// 尋找名稱包含 "Cash" 的帳戶
		if strings.Contains(strings.ToLower(account.Name), "cash") {
			if namedCashAccount == nil {
				namedCashAccount = &account
			}
		}

		// 尋找常見的現金帳戶代碼
		commonCashCodes := []string{"090", "100", "110", "1000", "1100"}
		for _, code := range commonCashCodes {
			if account.Code == code {
				if cashAccount == nil {
					cashAccount = &account
				}
			}
		}
	}

	// 按優先順序返回帳戶
	if bankAccount != nil {
		fmt.Printf("Found bank account for payments: %s (%s) - %s\n", bankAccount.Code, bankAccount.Name, bankAccount.AccountID)
		return bankAccount, nil
	}

	if namedCashAccount != nil {
		fmt.Printf("Found cash account by name: %s (%s) - %s\n", namedCashAccount.Code, namedCashAccount.Name, namedCashAccount.AccountID)
		return namedCashAccount, nil
	}

	if cashAccount != nil {
		fmt.Printf("Found cash account by code: %s (%s) - %s\n", cashAccount.Code, cashAccount.Name, cashAccount.AccountID)
		return cashAccount, nil
	}

	return nil, fmt.Errorf("no suitable cash account found")
}

// UpdateInvoice 更新發票
func (s *xeroService) UpdateInvoice(ctx context.Context, invoiceID string, invoice *domain.XeroInvoiceRequest) (*domain.XeroInvoiceResponse, error) {
	endpoint := fmt.Sprintf("/Invoices/%s", invoiceID)

	jsonData, err := json.Marshal(invoice)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal invoice: %v", err)
	}

	resp, err := s.makeXeroAPIRequestWithRetry(ctx, "PUT", endpoint, jsonData, false)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	fmt.Printf("UpdateInvoice response status: %d\n", resp.StatusCode)
	fmt.Printf("UpdateInvoice response body: %s\n", string(body))

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("Xero API error: %d - %s", resp.StatusCode, string(body))
	}

	var invoiceResp domain.XeroInvoiceResponse
	err = json.Unmarshal(body, &invoiceResp)
	if err != nil {
		return nil, fmt.Errorf("failed to parse invoice response: %v", err)
	}

	return &invoiceResp, nil
}

// GetInvoicePDF 獲取發票 PDF
func (s *xeroService) GetInvoicePDF(ctx context.Context, invoiceID string) ([]byte, error) {
	// 首先檢查發票是否存在
	invoice, err := s.GetInvoice(ctx, invoiceID)
	if err != nil {
		return nil, fmt.Errorf("failed to get invoice: %v", err)
	}

	if len(invoice.Invoices) == 0 {
		return nil, fmt.Errorf("invoice not found: %s", invoiceID)
	}

	// 檢查發票狀態
	invoiceData := invoice.Invoices[0]
	fmt.Printf("Invoice status: %s\n", invoiceData.Status)

	// 獲取有效的 token
	token, err := s.GetValidToken(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get valid token: %v", err)
	}

	// 使用正確的 Xero Files API 端點來獲取 PDF
	// 根據 Xero API 文檔，應該使用 Files API
	url := fmt.Sprintf("https://api.xero.com/files.xro/1.0/Invoices/%s", invoiceID)

	// 創建 HTTP 請求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}

	// 設置必要的 headers
	req.Header.Set("Authorization", "Bearer "+token.AccessToken)
	req.Header.Set("Xero-tenant-id", token.TenantID)
	req.Header.Set("Accept", "application/pdf")

	fmt.Printf("Requesting PDF for invoice: %s\n", invoiceID)
	fmt.Printf("Request URL: %s\n", url)

	// 發送請求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 檢查回應
	contentType := resp.Header.Get("Content-Type")
	fmt.Printf("Response Content-Type: %s\n", contentType)
	fmt.Printf("Response Status: %d\n", resp.StatusCode)

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		fmt.Printf("Error response body: %s\n", string(body))

		// 如果 Files API 失敗，嘗試使用標準 API 端點
		return s.getInvoicePDFAlternative(ctx, invoiceID, token)
	}

	// 檢查是否真的是 PDF
	if !strings.Contains(contentType, "application/pdf") {
		fmt.Printf("Warning: Response is not PDF, Content-Type: %s\n", contentType)
		// 嘗試替代方法
		return s.getInvoicePDFAlternative(ctx, invoiceID, token)
	}

	// 讀取 PDF 內容
	pdfData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read PDF data: %v", err)
	}

	// 驗證 PDF 數據
	if len(pdfData) < 100 {
		return nil, fmt.Errorf("PDF data too small, likely not a valid PDF")
	}

	// 檢查 PDF 魔術數字
	if len(pdfData) >= 4 && string(pdfData[:4]) != "%PDF" {
		fmt.Printf("Warning: Data does not start with PDF magic number\n")
		fmt.Printf("First 50 bytes: %s\n", string(pdfData[:min(50, len(pdfData))]))
		return nil, fmt.Errorf("received data is not a valid PDF")
	}

	fmt.Printf("PDF data size: %d bytes\n", len(pdfData))
	return pdfData, nil
}

// getInvoicePDFAlternative 使用替代方法獲取 PDF
func (s *xeroService) getInvoicePDFAlternative(ctx context.Context, invoiceID string, token *domain.XeroToken) ([]byte, error) {
	fmt.Printf("Attempting alternative PDF methods for invoice: %s\n", invoiceID)

	// 方法1: 嘗試使用標準 API 端點
	url := fmt.Sprintf("https://api.xero.com/api.xro/2.0/Invoices/%s", invoiceID)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Authorization", "Bearer "+token.AccessToken)
	req.Header.Set("Xero-tenant-id", token.TenantID)
	req.Header.Set("Accept", "application/pdf")

	fmt.Printf("Trying alternative PDF endpoint: %s\n", url)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	contentType := resp.Header.Get("Content-Type")
	fmt.Printf("Alternative response Content-Type: %s\n", contentType)

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		fmt.Printf("Alternative request failed: %d - %s\n", resp.StatusCode, string(body))

		// 如果標準方法也失敗，返回錯誤說明 Xero 不支持此發票的 PDF 導出
		return nil, fmt.Errorf("Xero API does not support PDF export for invoice %s. Status: %s. This may be because the invoice is in DRAFT status or has other restrictions", invoiceID, resp.Status)
	}

	pdfData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// 檢查回應內容
	fmt.Printf("Alternative response data size: %d bytes\n", len(pdfData))
	if len(pdfData) > 0 {
		fmt.Printf("First 100 chars: %s\n", string(pdfData[:min(100, len(pdfData))]))
	}

	// 如果仍然不是 PDF，返回詳細錯誤
	if len(pdfData) >= 4 && string(pdfData[:4]) != "%PDF" {
		return nil, fmt.Errorf("Xero API returned non-PDF data for invoice %s. Content-Type: %s. This invoice may need to be AUTHORISED or APPROVED before PDF export is available", invoiceID, contentType)
	}

	return pdfData, nil
}

// min 輔助函數
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// SendInvoiceEmail 發送發票 Email
func (s *xeroService) SendInvoiceEmail(ctx context.Context, invoiceID string, email string) error {
	// 獲取有效的 token
	token, err := s.GetValidToken(ctx)
	if err != nil {
		fmt.Printf("Failed to get valid token: %v\n", err)
		return fmt.Errorf("failed to get valid token: %v", err)
	}

	fmt.Printf("Using token for email: TenantID=%s, AccessToken length=%d\n",
		token.TenantID, len(token.AccessToken))

	// 構建 Email 請求 URL
	url := fmt.Sprintf("https://api.xero.com/api.xro/2.0/Invoices/%s/Email", invoiceID)

	// 嘗試最簡單的 Email 請求格式
	emailRequest := map[string]interface{}{
		"emailAddress": email,
	}

	jsonData, err := json.Marshal(emailRequest)
	if err != nil {
		return fmt.Errorf("failed to marshal email request: %v", err)
	}

	// 創建 HTTP 請求 - 嘗試使用 POST 方法
	req, err := http.NewRequest("POST", url, strings.NewReader(string(jsonData)))
	if err != nil {
		return err
	}

	// 設置必要的 headers
	req.Header.Set("Authorization", "Bearer "+token.AccessToken)
	req.Header.Set("Xero-tenant-id", token.TenantID)
	req.Header.Set("Content-Type", "application/json")

	fmt.Printf("Sending invoice email for invoice: %s to: %s\n", invoiceID, email)
	fmt.Printf("Request URL: %s\n", url)
	fmt.Printf("Request body: %s\n", string(jsonData))

	// 發送請求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// 檢查回應
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %v", err)
	}
	bodyStr := string(body)

	fmt.Printf("Email response status: %d\n", resp.StatusCode)
	fmt.Printf("Email response body: %s\n", bodyStr)

	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusCreated && resp.StatusCode != http.StatusNoContent {

		// 檢查是否是 Email 限制錯誤
		if strings.Contains(bodyStr, "Daily Email Rate Limit Exceeded") {
			return fmt.Errorf("Xero daily email limit exceeded. Please try again tomorrow or send the invoice manually from Xero")
		}

		// 檢查其他常見的 Email 錯誤
		if strings.Contains(bodyStr, "Email") && strings.Contains(bodyStr, "invalid") {
			return fmt.Errorf("Invalid email address. Please check the customer's email address")
		}

		return fmt.Errorf("Xero API error: %d - %s", resp.StatusCode, bodyStr)
	}

	fmt.Printf("Invoice email sent successfully\n")
	return nil
}

// CheckEmailLimitStatus 檢查 Xero Email 發送限制狀態
func (s *xeroService) CheckEmailLimitStatus(ctx context.Context) (bool, error) {
	// 獲取有效的 token
	token, err := s.GetValidToken(ctx)
	if err != nil {
		return false, fmt.Errorf("failed to get valid token: %v", err)
	}

	// 嘗試發送一個測試請求來檢查限制狀態
	// 使用 Organisation API 來檢查連接狀態，這個 API 調用不會消耗 Email 限制
	url := "https://api.xero.com/api.xro/2.0/Organisation"

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return false, err
	}

	req.Header.Set("Authorization", "Bearer "+token.AccessToken)
	req.Header.Set("Xero-tenant-id", token.TenantID)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return false, err
	}
	defer resp.Body.Close()

	// 如果 API 調用成功，表示連接正常
	// 實際的 Email 限制檢查需要嘗試發送 Email 才能知道
	// 這裡返回 true 表示 API 連接正常，可以嘗試發送 Email
	if resp.StatusCode == http.StatusOK {
		fmt.Printf("Xero API connection is healthy\n")
		return true, nil
	}

	body, _ := io.ReadAll(resp.Body)
	fmt.Printf("Xero API connection check failed: %d - %s\n", resp.StatusCode, string(body))
	return false, fmt.Errorf("Xero API connection failed: %d", resp.StatusCode)
}

// VoidInvoice 作廢發票
func (s *xeroService) VoidInvoice(ctx context.Context, invoiceID string) (*domain.XeroInvoiceResponse, error) {
	// 首先獲取發票當前狀態
	currentInvoice, err := s.GetInvoice(ctx, invoiceID)
	if err != nil {
		return nil, fmt.Errorf("failed to get invoice before voiding: %v", err)
	}

	if len(currentInvoice.Invoices) == 0 {
		return nil, fmt.Errorf("invoice not found: %s", invoiceID)
	}

	invoice := currentInvoice.Invoices[0]
	fmt.Printf("Current invoice status: %s\n", invoice.Status)

	// 檢查發票是否可以作廢
	if invoice.Status == "VOIDED" || invoice.Status == "DELETED" {
		fmt.Printf("Invoice %s is already voided/deleted\n", invoiceID)
		return currentInvoice, nil // 已經是作廢狀態，直接返回
	}

	if invoice.Status == "PAID" {
		return nil, fmt.Errorf("cannot void paid invoice %s - paid invoices cannot be voided in Xero", invoiceID)
	}

	// 根據發票狀態決定操作
	var targetStatus string
	var actionDescription string

	if invoice.Status == "DRAFT" {
		// DRAFT 狀態的發票只能刪除，不能作廢
		targetStatus = "DELETED"
		actionDescription = "delete"
	} else {
		// 其他狀態（AUTHORISED, SUBMITTED 等）可以作廢
		targetStatus = "VOIDED"
		actionDescription = "void"
	}

	fmt.Printf("Attempting to %s invoice %s (current status: %s -> %s)\n",
		actionDescription, invoiceID, invoice.Status, targetStatus)

	// 使用專門的方法來作廢發票
	response, err := s.voidInvoiceDirectly(ctx, invoiceID, targetStatus)
	if err != nil {
		return nil, fmt.Errorf("failed to %s invoice %s: %v", actionDescription, invoiceID, err)
	}

	fmt.Printf("Invoice %s %sed successfully\n", invoiceID, actionDescription)
	return response, nil
}

// voidInvoiceDirectly 直接作廢發票，使用最簡化的方法
func (s *xeroService) voidInvoiceDirectly(ctx context.Context, invoiceID string, targetStatus string) (*domain.XeroInvoiceResponse, error) {
	// 獲取有效的 token
	token, err := s.GetValidToken(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get valid token: %v", err)
	}

	// 構建包含 Invoices 數組的請求 body（符合 Xero API 格式）
	requestBody := map[string]interface{}{
		"Invoices": []map[string]interface{}{
			{
				"InvoiceID": invoiceID,
				"Status":    targetStatus,
			},
		},
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, err
	}

	// 構建 URL
	url := fmt.Sprintf("https://api.xero.com/api.xro/2.0/Invoices/%s", invoiceID)

	// 創建 HTTP 請求 - 使用 POST 方法
	req, err := http.NewRequest("POST", url, strings.NewReader(string(jsonData)))
	if err != nil {
		return nil, err
	}

	// 設置必要的 headers
	req.Header.Set("Authorization", "Bearer "+token.AccessToken)
	req.Header.Set("Xero-tenant-id", token.TenantID)
	req.Header.Set("Content-Type", "application/json")

	fmt.Printf("Voiding invoice directly: %s -> %s\n", invoiceID, targetStatus)
	fmt.Printf("Request body: %s\n", string(jsonData))

	// 發送請求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 讀取回應
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	fmt.Printf("Void response status: %d\n", resp.StatusCode)
	fmt.Printf("Void response body: %s\n", string(body))

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("Xero API error: %d - %s", resp.StatusCode, string(body))
	}

	var invoiceResp domain.XeroInvoiceResponse
	err = json.Unmarshal(body, &invoiceResp)
	if err != nil {
		return nil, fmt.Errorf("failed to parse invoice response: %v", err)
	}

	return &invoiceResp, nil
}

// addPaymentToInvoice 為 Invoice 添加付款記錄
func (s *xeroService) addPaymentToInvoice(ctx context.Context, invoiceID string, amount float64, paymentDate time.Time) error {
	// 獲取現金帳戶
	cashAccount, err := s.GetCashAccount(ctx)
	if err != nil {
		fmt.Printf("Failed to get cash account, trying without account specification: %v\n", err)
		// 如果無法獲取現金帳戶，嘗試不指定帳戶讓 Xero 使用預設帳戶
		return s.createPaymentWithoutAccount(ctx, invoiceID, amount, paymentDate)
	}

	// 創建付款記錄，使用找到的現金帳戶
	payment := &domain.XeroPaymentRequest{
		Payments: []domain.XeroPayment{
			{
				Invoice: struct {
					InvoiceID string `json:"InvoiceID"`
				}{
					InvoiceID: invoiceID,
				},
				Account: struct {
					Code string `json:"Code,omitempty"`
					Name string `json:"Name,omitempty"`
				}{
					Code: cashAccount.Code,
					Name: cashAccount.Name,
				},
				Date:      domain.XeroDate{Time: paymentDate},
				Amount:    amount,
				Reference: fmt.Sprintf("POS Payment for Invoice %s", invoiceID),
			},
		},
	}

	fmt.Printf("Creating payment with account: %s (%s)\n", cashAccount.Code, cashAccount.Name)

	// 調用 CreatePayment 方法
	_, err = s.CreatePayment(ctx, payment)
	if err != nil {
		fmt.Printf("Failed to create payment with account %s, trying without account: %v\n", cashAccount.Code, err)
		// 如果使用指定帳戶失敗，嘗試不指定帳戶
		return s.createPaymentWithoutAccount(ctx, invoiceID, amount, paymentDate)
	}

	fmt.Printf("Payment created for invoice %s: $%.2f\n", invoiceID, amount)
	return nil
}

// createPaymentWithoutAccount 創建付款記錄但不指定帳戶，讓 Xero 使用預設帳戶
func (s *xeroService) createPaymentWithoutAccount(ctx context.Context, invoiceID string, amount float64, paymentDate time.Time) error {
	payment := &domain.XeroPaymentRequest{
		Payments: []domain.XeroPayment{
			{
				Invoice: struct {
					InvoiceID string `json:"InvoiceID"`
				}{
					InvoiceID: invoiceID,
				},
				// 不指定 Account，讓 Xero 使用預設現金帳戶
				Date:      domain.XeroDate{Time: paymentDate},
				Amount:    amount,
				Reference: fmt.Sprintf("POS Payment for Invoice %s", invoiceID),
			},
		},
	}

	fmt.Printf("Creating payment without account specification\n")

	// 調用 CreatePayment 方法
	_, err := s.CreatePayment(ctx, payment)
	if err != nil {
		return fmt.Errorf("failed to create payment: %v", err)
	}

	fmt.Printf("Payment created for invoice %s: $%.2f\n", invoiceID, amount)
	return nil
}

// SyncOrderToXero 同步訂單到 Xero
func (s *xeroService) SyncOrderToXero(ctx context.Context, orderUUID string) (*domain.XeroInvoiceResponse, error) {
	// 取得訂單資訊
	orderRepo := repository.NewOrderRepository(s.db)
	xeroSyncRepo := repository.NewXeroOrderSyncRepository(s.db)

	order, err := orderRepo.GetByUUID(ctx, orderUUID)
	if err != nil {
		return nil, fmt.Errorf("failed to get order: %v", err)
	}

	// 檢查訂單狀態，只有已完成的訂單才同步
	if order.Status != "completed" {
		return nil, fmt.Errorf("order status is not completed: %s", order.Status)
	}

	// 檢查是否已經同步成功
	if order.XeroSync != nil && order.XeroSync.SyncStatus == domain.XeroSyncStatusSuccess {
		return nil, fmt.Errorf("order already synced to Xero: %s", order.XeroSync.XeroInvoiceID)
	}

	// 創建或更新同步記錄為同步中狀態
	if order.XeroSync == nil {
		syncRecord := &domain.XeroOrderSync{
			OrderUUID:  orderUUID,
			SyncStatus: domain.XeroSyncStatusSyncing,
		}
		err = xeroSyncRepo.Create(ctx, syncRecord)
		if err != nil {
			return nil, fmt.Errorf("failed to create sync record: %v", err)
		}
	} else {
		err = xeroSyncRepo.UpdateSyncStatus(ctx, orderUUID, domain.XeroSyncStatusSyncing, "")
		if err != nil {
			return nil, fmt.Errorf("failed to update sync status: %v", err)
		}
	}

	// 建立 Xero Contact
	contact := domain.XeroContact{
		Name: "Walk-in Customer", // 預設客戶名稱
	}

	// 如果有客戶資訊，使用客戶資訊
	if order.Customer.Name != "" {
		contact.Name = order.Customer.Name
		contact.EmailAddress = order.Customer.Email
	}

	// 建立 Line Items
	var lineItems []domain.XeroLineItem
	for _, item := range order.OrderItems {
		// 商品金額含稅，需要設定正確的 GST 計算
		lineItem := domain.XeroLineItem{
			Description: item.Product.Name,
			Quantity:    float64(item.Quantity),
			UnitAmount:  item.Price, // 含稅價格
			AccountCode: "200",      // 標準銷售收入帳戶
			TaxType:     "OUTPUT",   // GST 輸出稅 (含稅)
		}

		fmt.Printf("Line Item: %s, Qty: %.4f, Unit: %.2f (含稅), Account: %s, Tax: %s\n",
			lineItem.Description, lineItem.Quantity, lineItem.UnitAmount, lineItem.AccountCode, lineItem.TaxType)

		lineItems = append(lineItems, lineItem)
	}

	// 建立 Xero Invoice - 先創建為 AUTHORISED，然後添加付款
	invoice := domain.XeroInvoice{
		Type:            "ACCREC", // 應收帳款
		Contact:         contact,
		Date:            domain.XeroDate{Time: order.OrderAt},
		DueDate:         domain.XeroDate{Time: order.OrderAt.AddDate(0, 0, 30)}, // 30天後到期
		Status:          "AUTHORISED",                                           // 先設為授權狀態，然後添加付款
		LineAmountTypes: "Inclusive",                                            // 含稅金額
		LineItems:       lineItems,
		Reference:       fmt.Sprintf("POS-%s", order.OrderNo),
	}

	// 建立請求
	invoiceRequest := &domain.XeroInvoiceRequest{
		Invoices: []domain.XeroInvoice{invoice},
	}

	// 調試信息：打印即將發送的 Invoice 數據
	fmt.Printf("Creating Xero Invoice:\n")
	fmt.Printf("  Type: %s\n", invoice.Type)
	fmt.Printf("  Contact: %s\n", invoice.Contact.Name)
	fmt.Printf("  Status: %s\n", invoice.Status)
	fmt.Printf("  LineAmountTypes: %s (含稅金額)\n", invoice.LineAmountTypes)
	fmt.Printf("  Reference: %s\n", invoice.Reference)
	fmt.Printf("  Total Line Items: %d\n", len(invoice.LineItems))
	fmt.Printf("  Will add payment after creation since customer paid at POS\n")

	// 發送到 Xero
	response, err := s.CreateInvoice(ctx, invoiceRequest)
	if err != nil {
		// 更新同步狀態為失敗
		syncErr := xeroSyncRepo.UpdateSyncStatus(ctx, orderUUID, domain.XeroSyncStatusFailed, err.Error())
		if syncErr != nil {
			fmt.Printf("Failed to update sync status: %v\n", syncErr)
		}
		return nil, fmt.Errorf("failed to create invoice in Xero: %v", err)
	}

	// 如果發票創建成功，添加付款記錄
	if len(response.Invoices) > 0 {
		createdInvoice := response.Invoices[0]
		fmt.Printf("Invoice created successfully: %s (Status: %s)\n", createdInvoice.InvoiceID, createdInvoice.Status)

		// 計算訂單總金額
		var totalAmount float64
		for _, item := range order.OrderItems {
			totalAmount += item.Price * float64(item.Quantity)
		}

		// 添加付款記錄，將 Invoice 標記為已付款
		err = s.addPaymentToInvoice(ctx, createdInvoice.InvoiceID, totalAmount, order.OrderAt)
		if err != nil {
			fmt.Printf("Failed to add payment to invoice: %v\n", err)
			// 即使付款添加失敗，Invoice 已經創建成功
		} else {
			fmt.Printf("Payment added successfully, Invoice is now PAID\n")
		}

		// 更新同步記錄
		err = xeroSyncRepo.UpdateSyncSuccess(ctx, orderUUID, createdInvoice.InvoiceID, createdInvoice.InvoiceNumber)
		if err != nil {
			fmt.Printf("Failed to update sync success: %v\n", err)
		}
	}

	return response, nil
}

// VoidOrderInXero 在 Xero 中作廢訂單對應的發票
func (s *xeroService) VoidOrderInXero(ctx context.Context, orderUUID string) error {
	fmt.Printf("Starting void process for order: %s\n", orderUUID)

	xeroSyncRepo := repository.NewXeroOrderSyncRepository(s.db)

	// 首先從同步記錄中查找 Xero Invoice ID
	syncRecord, err := xeroSyncRepo.GetByOrderUUID(ctx, orderUUID)
	if err != nil {
		fmt.Printf("No sync record found for order %s: %v\n", orderUUID, err)
		fmt.Printf("Falling back to reference-based lookup\n")
		return s.voidOrderByReference(ctx, orderUUID)
	}

	// 檢查同步狀態
	if syncRecord.SyncStatus != domain.XeroSyncStatusSuccess {
		return fmt.Errorf("order %s was not successfully synced to Xero (status: %s)", orderUUID, syncRecord.SyncStatus)
	}

	if syncRecord.XeroInvoiceID == "" {
		return fmt.Errorf("no Xero invoice ID found for order %s", orderUUID)
	}

	fmt.Printf("Voiding Xero invoice %s for order %s\n", syncRecord.XeroInvoiceID, orderUUID)

	// 作廢發票
	_, err = s.VoidInvoice(ctx, syncRecord.XeroInvoiceID)
	if err != nil {
		// 更新同步狀態為失敗
		syncErr := xeroSyncRepo.UpdateSyncStatus(ctx, orderUUID, domain.XeroSyncStatusFailed,
			fmt.Sprintf("Failed to void invoice: %v", err))
		if syncErr != nil {
			fmt.Printf("Failed to update sync status: %v\n", syncErr)
		}
		return fmt.Errorf("failed to void invoice in Xero: %v", err)
	}

	// 更新同步狀態為已作廢
	err = xeroSyncRepo.UpdateSyncStatus(ctx, orderUUID, domain.XeroSyncStatusVoided, "Invoice voided/deleted successfully")
	if err != nil {
		fmt.Printf("Failed to update sync status to voided: %v\n", err)
	}

	fmt.Printf("Invoice %s voided successfully\n", syncRecord.XeroInvoiceID)
	return nil
}

// CreatePayment 創建付款記錄
func (s *xeroService) CreatePayment(ctx context.Context, payment *domain.XeroPaymentRequest) (*domain.XeroPaymentResponse, error) {
	bodyBytes, err := json.Marshal(payment)
	if err != nil {
		return nil, err
	}

	resp, err := s.makeXeroAPIRequestWithRetry(ctx, "POST", "/Payments", bodyBytes, false)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusCreated {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("Xero API error: %d - %s", resp.StatusCode, string(body))
	}

	var response domain.XeroPaymentResponse
	err = json.NewDecoder(resp.Body).Decode(&response)
	return &response, err
}

// createPaymentForInvoice 為發票創建付款記錄
func (s *xeroService) createPaymentForInvoice(ctx context.Context, invoiceID string, amount float64, paymentDate time.Time) error {
	payment := domain.XeroPayment{
		Date:      domain.XeroDate{Time: paymentDate},
		Amount:    amount,
		Reference: "POS Payment",
		Invoice: struct {
			InvoiceID string `json:"InvoiceID"`
		}{
			InvoiceID: invoiceID,
		},
		// 不設定 Account，讓 Xero 使用預設現金帳戶
	}

	paymentRequest := &domain.XeroPaymentRequest{
		Payments: []domain.XeroPayment{payment},
	}

	fmt.Printf("Creating payment for invoice %s, amount: %.2f\n", invoiceID, amount)

	_, err := s.CreatePayment(ctx, paymentRequest)
	if err != nil {
		return fmt.Errorf("failed to create payment: %v", err)
	}

	fmt.Printf("Payment created successfully for invoice %s\n", invoiceID)
	return nil
}

// voidOrderByReference 通過 Reference 查找並作廢發票（後備方案）
func (s *xeroService) voidOrderByReference(ctx context.Context, orderUUID string) error {
	// 取得訂單資訊
	orderRepo := repository.NewOrderRepository(s.db)
	order, err := orderRepo.GetByUUID(ctx, orderUUID)
	if err != nil {
		return fmt.Errorf("failed to get order: %v", err)
	}

	// 通過 Reference 查找對應的 Invoice
	params := &domain.XeroInvoiceParams{}
	invoicesResp, err := s.GetInvoices(ctx, params)
	if err != nil {
		return fmt.Errorf("failed to get invoices: %v", err)
	}

	// 查找匹配的發票
	var targetInvoiceID string
	reference := fmt.Sprintf("POS-%s", order.OrderNo)

	fmt.Printf("Searching for invoice with reference: %s\n", reference)

	for _, invoice := range invoicesResp.Invoices {
		if invoice.Reference == reference {
			targetInvoiceID = invoice.InvoiceID
			fmt.Printf("Found matching invoice: %s\n", targetInvoiceID)
			break
		}
	}

	if targetInvoiceID == "" {
		return fmt.Errorf("invoice not found for order: %s (reference: %s)", orderUUID, reference)
	}

	// 作廢發票
	_, err = s.VoidInvoice(ctx, targetInvoiceID)
	if err != nil {
		return fmt.Errorf("failed to void invoice: %v", err)
	}

	fmt.Printf("Invoice %s voided successfully via reference lookup\n", targetInvoiceID)
	return nil
}
