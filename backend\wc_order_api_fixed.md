# WooCommerce 訂單 API 修正版 - 使用 wp_wc_orders 表

## 🚨 重要修正

您完全正確！我之前犯了一個重大錯誤。現代版本的 WooCommerce 使用 `wp_wc_orders` 表來存儲訂單，而不是 `wp_posts` 表。我已經完全重寫了查詢邏輯。

## 主要修正

### 1. 使用正確的資料庫表
- **之前（錯誤）**: 從 `wp_posts` 表查詢訂單
- **現在（正確）**: 從 `wp_wc_orders` 表查詢訂單

### 2. 正確的查詢結構
參考 wcapi.php 中的 `FROM wp_wc_orders o`：

```sql
SELECT 
    o.id,
    o.date_created_gmt as date_created,
    o.date_modified_gmt as date_modified,
    o.status,
    o.total_amount as total,
    o.billing_first_name,
    o.billing_last_name,
    o.billing_email,
    o.payment_method_title,
    o.customer_id,
    o.currency,
    o.shipping_total,
    o.tax_amount as tax_total
FROM 
    wp_wc_orders o
WHERE 
    1=1
```

### 3. 狀態格式處理
- **wp_wc_orders 表中的狀態**: `pending`, `processing`, `completed` 等（無 wc- 前綴）
- **前端期望的格式**: `wc-pending`, `wc-processing`, `wc-completed` 等
- **自動轉換**: API 會自動處理狀態格式轉換

## 修正的功能

### 1. 訂單列表查詢 (`GetOrdersWithStandardQuery`)
```sql
SELECT 
    o.id,
    o.date_created_gmt as date_created,
    o.date_modified_gmt as date_modified,
    o.status,
    o.total_amount as total,
    o.billing_first_name,
    o.billing_last_name,
    o.billing_email,
    o.payment_method_title,
    o.customer_id,
    o.currency,
    o.shipping_total,
    o.tax_amount as tax_total,
    o.billing_phone,
    o.billing_address_1,
    o.billing_city,
    o.customer_note
FROM 
    wp_wc_orders o
WHERE 
    1=1
ORDER BY o.date_created_gmt DESC
```

### 2. 訂單詳情查詢 (`GetByID`)
直接從 `wp_wc_orders` 表獲取完整的訂單資訊，包括：
- 基本資訊（ID、日期、狀態、總金額）
- 客戶資訊（姓名、郵箱、電話、地址）
- 付款資訊（付款方式、交易ID）
- 運送資訊（運費、運送地址）

### 3. 訂單狀態更新 (`UpdateStatus`)
```sql
UPDATE wp_wc_orders 
SET status = ?, date_modified_gmt = NOW()
WHERE id = ?
```

### 4. 改進的診斷功能
- 優先檢查 `wp_wc_orders` 表是否存在
- 如果不存在，回退檢查舊的 `wp_posts` 表
- 顯示正確的訂單統計和狀態分佈

## API 端點測試

### 1. 診斷端點（必須先測試）
```bash
curl -X GET "http://localhost:8080/api/v1/wc-orders/diagnose" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

預期回應應該顯示：
```json
{
  "success": true,
  "diagnosis": {
    "database_connection": "OK",
    "table_prefix": "wp_",
    "tables_exist": {
      "wp_wc_orders": true,
      "wp_posts": true,
      "wp_postmeta": true
    },
    "wc_orders_count": 50,
    "status_distribution": [
      {"status": "pending", "count": 10},
      {"status": "processing", "count": 15},
      {"status": "completed", "count": 25}
    ],
    "recent_orders": [...]
  }
}
```

### 2. 標準查詢端點
```bash
curl -X GET "http://localhost:8080/api/v1/wc-orders/standard?limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. 待處理訂單
```bash
curl -X GET "http://localhost:8080/api/v1/wc-orders/pending" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 4. 歷史訂單
```bash
curl -X GET "http://localhost:8080/api/v1/wc-orders/history" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 5. 訂單詳情
```bash
curl -X GET "http://localhost:8080/api/v1/wc-orders/123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 6. 更新訂單狀態
```bash
curl -X PATCH "http://localhost:8080/api/v1/wc-orders/123/status" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"status": "processing"}'
```

## 與 wcapi.php 的完全對應

| wcapi.php 查詢 | 新的 API 實作 | 說明 |
|----------------|---------------|------|
| `FROM wp_wc_orders o` | `FROM wp_wc_orders o` | ✅ 完全一致 |
| `o.status` | `o.status` | ✅ 狀態字段一致 |
| `o.total_amount` | `o.total_amount` | ✅ 總金額字段一致 |
| `o.date_created_gmt` | `o.date_created_gmt` | ✅ 日期字段一致 |

## 重要注意事項

1. **表結構**: 現在使用正確的 `wp_wc_orders` 表
2. **狀態格式**: 自動處理 `pending` ↔ `wc-pending` 轉換
3. **數據類型**: `wp_wc_orders` 表中的數值字段已經是正確類型，無需字符串轉換
4. **向後兼容**: 如果 `wp_wc_orders` 表不存在，會回退到檢查 `wp_posts` 表

這個修正版本應該能正確地從您的 WooCommerce 資料庫中獲取訂單資料！
