# WooCommerce 訂單整合測試指南 (修正版)

## 問題診斷與解決

### 發現的問題
1. **原始實作過於複雜**：嘗試處理所有 WooCommerce 表結構和元數據
2. **表前綴檢測**：不同 WordPress 安裝可能使用不同的表前綴
3. **WooCommerce 版本差異**：不同版本的 WooCommerce 可能有不同的資料庫結構

### 解決方案
建立了簡化版本的 WooCommerce 訂單 API，專注於核心功能：

## 新的簡化 API 端點

### 1. 診斷資料庫
- **端點**: `GET /api/v1/wc-orders-simple/diagnose`
- **說明**: 檢查資料庫連接和 WooCommerce 訂單資料
- **用途**: 診斷為什麼無法獲取訂單資料

### 2. 獲取所有訂單
- **端點**: `GET /api/v1/wc-orders-simple`
- **說明**: 獲取所有 WooCommerce 訂單

### 3. 獲取歷史訂單
- **端點**: `GET /api/v1/wc-orders-simple/history`
- **說明**: 獲取所有歷史訂單列表

### 4. 獲取待處理訂單
- **端點**: `GET /api/v1/wc-orders-simple/pending`
- **說明**: 獲取所有待處理訂單列表

### 5. 獲取訂單詳細資料
- **端點**: `GET /api/v1/wc-orders-simple/{id}`
- **說明**: 獲取指定訂單的詳細資料

### 6. 更新訂單狀態
- **端點**: `PATCH /api/v1/wc-orders-simple/{id}/status`
- **請求體**: `{"status": "wc-processing"}`

## 測試步驟

### 1. 啟動後端服務
```bash
cd backend
go run main.go
```

### 2. 測試診斷端點
首先測試診斷端點來檢查資料庫狀況：

```bash
curl -X GET "http://localhost:8080/api/v1/wc-orders-simple/diagnose" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

預期回應：
```json
{
  "success": true,
  "diagnosis": {
    "database_connection": "OK",
    "shop_order_count": 10,
    "status_distribution": [
      {"status": "wc-completed", "count": 5},
      {"status": "wc-processing", "count": 3},
      {"status": "wc-pending", "count": 2}
    ],
    "recent_orders": [
      {"id": 123, "post_date": "2024-01-01T10:00:00Z", "post_status": "wc-completed"}
    ]
  }
}
```

### 3. 測試訂單列表
```bash
curl -X GET "http://localhost:8080/api/v1/wc-orders-simple" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 4. 測試前端整合
前端 API 調用已更新為使用簡化版本的端點：
- `OrderApi.listWCHistory()` → `/v1/wc-orders-simple/history`
- `OrderApi.wcFetchPending()` → `/v1/wc-orders-simple/pending`
- `OrderApi.getWCOrder(id)` → `/v1/wc-orders-simple/${id}`
- `OrderApi.diagnoseDatabaseWC()` → `/v1/wc-orders-simple/diagnose` (新增)

## 故障排除

### 如果診斷端點顯示 shop_order_count: 0
1. 檢查 WordPress 資料庫是否包含 WooCommerce 訂單
2. 確認表前綴是否正確（可能不是 `wp_`）
3. 檢查 WooCommerce 是否已安裝並有訂單資料

### 如果資料庫連接失敗
1. 檢查 `.env` 文件中的 `WP_DB_NAME` 設定
2. 確認資料庫連接參數正確
3. 檢查資料庫服務是否運行

### 如果訂單資料不完整
簡化版本只獲取基本的訂單資訊：
- 訂單 ID、日期、狀態
- 基本客戶資訊（姓名、郵箱）
- 訂單總金額
- 付款方式

## 下一步

1. **測試診斷端點**確認資料庫連接和資料存在
2. **檢查診斷結果**了解實際的資料庫結構
3. **根據診斷結果調整**查詢邏輯
4. **逐步完善功能**添加更多訂單詳細資訊

## 檔案結構

```
backend/
├── repository/
│   ├── wc_order_repo.go (原始複雜版本)
│   └── wc_order_simple_repo.go (簡化版本)
├── service/
│   ├── wc_order_service.go (原始版本)
│   └── wc_order_simple_service.go (簡化版本)
├── controller/
│   ├── wc_order_controller.go (原始版本)
│   └── wc_order_simple_controller.go (簡化版本)
└── debug_wc_db.go (獨立診斷工具)
```

兩個版本並存，可以根據測試結果選擇使用哪個版本。
