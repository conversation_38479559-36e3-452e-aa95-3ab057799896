<template>
  <q-page class="q-pa-md">
    <div class="row justify-center">
      <div class="col-12 col-md-8 col-lg-6">
        <q-card class="q-pa-md">
          <q-card-section>
            <div class="text-h6 q-mb-md">{{ $t('xero.setup.title') }}</div>

            <!-- 連接狀態顯示 -->
            <q-banner
              v-if="connectionStatus.connected"
              class="bg-positive text-white q-mb-md"
              rounded
            >
              <template v-slot:avatar>
                <q-icon name="check_circle" color="white" />
              </template>
              {{
                $t('xero.setup.connectionStatus.connected', {
                  tenantName: connectionStatus.tenant_name,
                })
              }}
              <br />
              <small>
                {{
                  $t('xero.setup.connectionStatus.tokenExpiry', {
                    expiryDate: formatDate(connectionStatus.expires_at),
                  })
                }}
              </small>

              <template v-slot:action>
                <q-btn
                  flat
                  color="white"
                  :label="$t('xero.setup.connectionStatus.disconnect')"
                  @click="disconnectXero"
                  :loading="disconnecting"
                />
                <q-btn
                  flat
                  color="white"
                  :label="$t('xero.setup.connectionStatus.refreshToken')"
                  @click="refreshToken"
                  :loading="refreshing"
                />
              </template>
            </q-banner>

            <q-banner v-else class="bg-warning text-dark q-mb-md" rounded>
              <template v-slot:avatar>
                <q-icon name="warning" color="orange" />
              </template>
              {{ $t('xero.setup.connectionStatus.notConnected') }}
            </q-banner>
          </q-card-section>

          <q-card-section>
            <!-- Xero設定表單 -->
            <q-form @submit="saveConfig" class="q-gutter-md">
              <q-input
                v-model="form.client_id"
                filled
                :label="$t('xero.setup.form.clientId') + ' *'"
                lazy-rules
                :rules="[
                  (val) =>
                    (val && val.length > 0) ||
                    $t('xero.setup.validation.clientIdRequired'),
                ]"
              />

              <q-input
                v-model="form.client_secret"
                filled
                type="password"
                :label="$t('xero.setup.form.clientSecret') + ' *'"
                lazy-rules
                :rules="[
                  (val) =>
                    (val && val.length > 0) ||
                    $t('xero.setup.validation.clientSecretRequired'),
                ]"
              />

              <q-input
                v-model="form.redirect_uri"
                filled
                :label="$t('xero.setup.form.redirectUri') + ' *'"
                lazy-rules
                :rules="[
                  (val) =>
                    (val && val.length > 0) ||
                    $t('xero.setup.validation.redirectUriRequired'),
                ]"
              />

              <q-input
                v-model="form.scopes"
                filled
                :label="$t('xero.setup.form.scopes')"
                :hint="$t('xero.setup.form.scopesHint')"
                :placeholder="$t('xero.setup.form.scopesPlaceholder')"
              >
                <template v-slot:append>
                  <q-btn
                    flat
                    round
                    dense
                    icon="info"
                    @click="showScopeInfo = true"
                  />
                </template>
              </q-input>

              <!-- Scope 權限說明 -->
              <q-card v-if="scopeValidation.length > 0" flat bordered class="q-mt-sm">
                <q-card-section class="q-pa-sm">
                  <div class="text-caption text-weight-medium q-mb-xs">權限檢查:</div>
                  <div v-for="validation in scopeValidation" :key="validation.scope" class="row items-center q-mb-xs">
                    <q-icon
                      :name="validation.valid ? 'check_circle' : 'error'"
                      :color="validation.valid ? 'positive' : 'negative'"
                      size="xs"
                      class="q-mr-sm"
                    />
                    <span class="text-caption">{{ validation.description }}</span>
                  </div>
                </q-card-section>
              </q-card>

              <div class="row q-gutter-sm">
                <q-btn
                  :label="$t('save')"
                  type="submit"
                  color="primary"
                  :loading="saving"
                />

                <q-btn
                  :label="$t('xero.setup.form.connectXero')"
                  color="secondary"
                  :disable="!isConfigValid || connectionStatus.connected"
                  @click="connectToXero"
                  :loading="connecting"
                />
              </div>
            </q-form>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- Scope 權限說明對話框 -->
    <q-dialog v-model="showScopeInfo">
      <q-card style="min-width: 400px">
        <q-card-section>
          <div class="text-h6">Xero API 權限說明</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <div class="q-mb-md">
            <div class="text-subtitle2 text-weight-medium q-mb-sm">必需權限 (取得發票):</div>
            <q-chip color="positive" text-color="white" icon="check_circle">
              accounting.transactions
            </q-chip>
            <div class="text-caption q-mt-xs">允許讀取和管理發票、帳單、付款等所有交易記錄</div>
          </div>

          <div class="q-mb-md">
            <div class="text-subtitle2 text-weight-medium q-mb-sm">建議權限:</div>
            <q-chip color="info" text-color="white" icon="people">
              accounting.contacts
            </q-chip>
            <div class="text-caption q-mt-xs">允許讀取和管理客戶聯絡人資料</div>

            <q-chip color="orange" text-color="white" icon="settings" class="q-mt-sm">
              accounting.settings
            </q-chip>
            <div class="text-caption q-mt-xs">允許讀取組織設定、稅率、科目等基礎資料</div>
          </div>

          <div class="q-mb-md">
            <div class="text-subtitle2 text-weight-medium q-mb-sm">推薦設定:</div>
            <q-input
              readonly
              filled
              model-value="accounting.transactions accounting.contacts accounting.settings"
              label="完整權限設定"
            >
              <template v-slot:append>
                <q-btn
                  flat
                  round
                  dense
                  icon="content_copy"
                  @click="copyToClipboard('accounting.transactions accounting.contacts accounting.settings')"
                />
              </template>
            </q-input>
          </div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="關閉" color="primary" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useQuasar } from 'quasar';
import { XeroApi } from '@/api/xero';
import { formatDate } from '@/utils';
import { usePageInfoStore } from '@/stores/pageInfo';

const { t } = useI18n();
const $q = useQuasar();
const { setPageTitle } = usePageInfoStore();

// 設置頁面標題
setPageTitle(t('xero.setup.title'));

// 響應式資料
const form = reactive({
  client_id: '',
  client_secret: '',
  redirect_uri: window.location.origin + '/xero/redirect',
  scopes: 'accounting.transactions accounting.contacts',
});

const connectionStatus = reactive({
  connected: false,
  tenant_name: '',
  tenant_id: '',
  expires_at: '',
});

const saving = ref(false);
const connecting = ref(false);
const refreshing = ref(false);
const disconnecting = ref(false);
const showScopeInfo = ref(false);

// 計算屬性
const isConfigValid = computed(() => {
  return form.client_id && form.client_secret && form.redirect_uri;
});

const scopeValidation = computed(() => {
  const scopes = form.scopes.toLowerCase().split(' ').filter(s => s.length > 0);
  const validations = [];

  // 檢查 accounting.transactions (發票必需)
  const hasTransactions = scopes.includes('accounting.transactions');
  validations.push({
    scope: 'accounting.transactions',
    valid: hasTransactions,
    description: hasTransactions
      ? '✓ 可以讀取和管理發票、帳單等交易'
      : '✗ 缺少 accounting.transactions - 無法讀取發票'
  });

  // 檢查 accounting.contacts (聯絡人)
  const hasContacts = scopes.includes('accounting.contacts');
  validations.push({
    scope: 'accounting.contacts',
    valid: hasContacts,
    description: hasContacts
      ? '✓ 可以讀取和管理聯絡人'
      : '✗ 缺少 accounting.contacts - 無法讀取客戶資料'
  });

  // 檢查 accounting.settings (可選但建議)
  const hasSettings = scopes.includes('accounting.settings');
  validations.push({
    scope: 'accounting.settings',
    valid: hasSettings,
    description: hasSettings
      ? '✓ 可以讀取組織設定和稅率'
      : 'ⓘ 建議添加 accounting.settings - 用於讀取稅率和組織設定'
  });

  return validations;
});

// 方法
const loadConfig = async () => {
  const response = await XeroApi.getConfig();
  if (response.result) {
    Object.assign(form, response.result);
  }
};

const loadConnectionStatus = async () => {
  const response = await XeroApi.getConnectionStatus();
  Object.assign(connectionStatus, response.result);
};

const saveConfig = async () => {
  saving.value = true;
  try {
    await XeroApi.saveConfig(form);
    $q.notify({
      position: 'top',
      type: 'positive',
      message: t('success'),
    });
  } finally {
    saving.value = false;
  }
};

const connectToXero = async () => {
  connecting.value = true;
  try {
    // 先儲存設定
    await saveConfig();

    // 取得認證URL
    const response = await XeroApi.getAuthURL();
    const { auth_url, state } = response.result;

    // 儲存state到localStorage，並驗證是否成功保存
    localStorage.setItem('xero_oauth_state', state);
    const savedState = localStorage.getItem('xero_oauth_state');

    if (savedState !== state) {
      console.error('Failed to save state to localStorage');
      $q.notify({
        position: 'top',
        type: 'warning',
        message: t('xero.setup.error.stateStorageFailed'),
      });
    }

    // 跳轉到Xero認證頁面
    window.location.href = auth_url;
  } catch (error) {
    console.error('Connect to Xero error:', error);
    $q.notify({
      position: 'top',
      type: 'negative',
      message: (error as Error)?.message || t('failed'),
    });
  } finally {
    connecting.value = false;
  }
};

const refreshToken = async () => {
  refreshing.value = true;
  try {
    await XeroApi.refreshToken();
    await loadConnectionStatus();
    $q.notify({
      position: 'top',
      type: 'positive',
      message: t('success'),
    });
  } finally {
    refreshing.value = false;
  }
};

const disconnectXero = async () => {
  $q.dialog({
    title: t('xero.setup.dialog.disconnectTitle'),
    message: t('xero.setup.dialog.disconnectMessage'),
    cancel: true,
    persistent: true,
  }).onOk(async () => {
    disconnecting.value = true;
    try {
      await XeroApi.disconnect();
      await loadConnectionStatus();
      $q.notify({
        position: 'top',
        type: 'positive',
        message: t('success'),
      });
    } finally {
      disconnecting.value = false;
    }
  });
};

const copyToClipboard = (text: string) => {
  navigator.clipboard.writeText(text).then(() => {
    $q.notify({
      type: 'positive',
      message: 'copy to clipboard',
      position: 'top'
    });
  });
};

// 生命週期
onMounted(() => {
  loadConfig();
  loadConnectionStatus();
});
</script>
