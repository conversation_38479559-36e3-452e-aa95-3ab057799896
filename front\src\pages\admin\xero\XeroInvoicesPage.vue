<template>
  <q-page class="q-pa-md">
    <div class="row justify-center">
      <div class="col-12">
        <q-card>
          <q-card-section>
            <div class="text-h6 q-mb-md">{{ $t('xero.invoices.title') }}</div>

            <!-- 連接狀態檢查 -->
            <q-banner
              v-if="!connectionStatus.connected"
              class="bg-warning text-dark q-mb-md"
              rounded
            >
              <template v-slot:avatar>
                <q-icon name="warning" color="orange" />
              </template>
              {{ $t('xero.invoices.notConnected') }}
              <template v-slot:action>
                <q-btn
                  flat
                  color="dark"
                  :label="$t('xero.invoices.goToSetup')"
                  @click="goToSetup"
                />
              </template>
            </q-banner>

            <!-- 篩選器 -->
            <div
              v-if="connectionStatus.connected"
              class="row q-gutter-md q-mb-md"
            >
              <q-select
                v-model="filters.status"
                :options="statusOptions"
                :label="$t('status')"
                clearable
                style="min-width: 150px"
                map-options
                emit-value
                dense
                @update:model-value="loadInvoices"
              />

              <DateRangePicker
                v-model="filters.dateRange"
                dateMask="YYYY-MM-DD"
                @update:model-value="loadInvoices"
              />
            </div>

            <!-- 發票列表 -->
            <q-table
              v-if="connectionStatus.connected"
              :rows="invoices"
              :columns="columns"
              v-model:pagination="pagination"
              @request="onRequest"
              row-key="invoice_id"
              binary-state-sort
              :loading="loading"
            >
              <template v-slot:body-cell-status="props">
                <q-td :props="props">
                  <q-chip
                    :color="getStatusColor(props.value)"
                    text-color="white"
                    :label="
                      $t(`xero.invoices.status.${props.value.toLowerCase()}`)
                    "
                  />
                </q-td>
              </template>

              <template v-slot:body-cell-type="props">
                <q-td :props="props">
                  <q-chip
                    :color="props.value === 'ACCREC' ? 'positive' : 'info'"
                    text-color="white"
                    :label="
                      $t(`xero.invoices.type.${props.value.toLowerCase()}`)
                    "
                  />
                </q-td>
              </template>

              <template v-slot:body-cell-total="props">
                <q-td :props="props">
                  AU {{ formatCurrency(props.value, props.row.currency_code) }}
                </q-td>
              </template>

              <template v-slot:body-cell-actions="props">
                <q-td :props="props">
                  <q-btn
                    flat
                    round
                    color="primary"
                    icon="visibility"
                    size="sm"
                    @click="viewInvoice(props.row)"
                  >
                    <q-tooltip>{{ $t('view') }}</q-tooltip>
                  </q-btn>
                </q-td>
              </template>
            </q-table>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- 發票詳情對話框 -->
    <q-dialog v-model="invoiceDialog" class="card-dialog">
      <q-card class="column">
        <q-card-section class="col-1 q-py-none">
          <div class="row">
            <div class="text-h6">{{ $t('xero.invoices.invoiceDetails') }}</div>
            <q-space />
            <q-btn
              type="button"
              icon="close"
              flat
              round
              dense
              @click="invoiceDialog = false"
            />
          </div>
        </q-card-section>

        <q-card-section class="col-10" v-if="selectedInvoice">
          <!-- 基本資料區塊 -->
          <div class="q-mb-md bg-white rounded-borders" style="border: 1px solid #e0e0e0;">
            <div class="row">
              <!-- Invoice Number -->
              <q-item class="col-12 col-md-6" v-if="selectedInvoice.invoice_number">
                <q-item-section>
                  <q-item-label class="text-weight-medium text-grey-7 text-caption">{{
                    $t('xero.invoices.invoiceNumber')
                  }}</q-item-label>
                  <q-item-label class="text-subtitle1 text-grey-9">{{
                    selectedInvoice.invoice_number
                  }}</q-item-label>
                </q-item-section>
              </q-item>

              <!-- Status -->
              <q-item class="col-12 col-md-6">
                <q-item-section>
                  <q-item-label class="text-weight-medium text-grey-7 text-caption">{{ $t('status') }}</q-item-label>
                  <q-item-label>
                    <q-chip
                      :color="getStatusColor(selectedInvoice.status)"
                      text-color="white"
                      class="text-subtitle1"
                      size="md"
                    >
                      {{ getStatusLabel(selectedInvoice.status) }}
                    </q-chip>
                  </q-item-label>
                </q-item-section>
              </q-item>

              <!-- Contact -->
              <q-item class="col-12 col-md-6">
                <q-item-section>
                  <q-item-label class="text-weight-medium text-grey-7 text-caption">{{
                    $t('xero.invoices.contact')
                  }}</q-item-label>
                  <q-item-label class="text-subtitle1 text-grey-9">{{
                    selectedInvoice.contact?.name || ''
                  }}</q-item-label>
                </q-item-section>
              </q-item>

              <!-- Date -->
              <q-item class="col-12 col-md-6">
                <q-item-section>
                  <q-item-label class="text-weight-medium text-grey-7 text-caption">{{
                    $t('xero.invoices.date')
                  }}</q-item-label>
                  <q-item-label class="text-subtitle1 text-grey-9">{{
                    formatDate(selectedInvoice.date)
                  }}</q-item-label>
                </q-item-section>
              </q-item>

              <!-- Due Date -->
              <q-item class="col-12 col-md-6">
                <q-item-section>
                  <q-item-label class="text-weight-medium text-grey-7 text-caption">{{
                    $t('xero.invoices.dueDate')
                  }}</q-item-label>
                  <q-item-label class="text-subtitle1 text-grey-9">{{
                    formatDate(selectedInvoice.due_date)
                  }}</q-item-label>
                </q-item-section>
              </q-item>

              <!-- Sub Total -->
              <q-item class="col-12 col-md-6">
                <q-item-section>
                  <q-item-label class="text-weight-medium text-grey-7 text-caption">{{
                    $t('xero.invoices.subTotal')
                  }}</q-item-label>
                  <q-item-label class="text-subtitle1 text-grey-9">AU {{
                    formatCurrency(
                      selectedInvoice.sub_total,
                      selectedInvoice.currency_code
                    )
                  }}</q-item-label>
                </q-item-section>
              </q-item>

              <!-- Total Tax -->
              <q-item class="col-12 col-md-6">
                <q-item-section>
                  <q-item-label class="text-weight-medium text-grey-7 text-caption">{{
                    $t('xero.invoices.totalTax')
                  }}</q-item-label>
                  <q-item-label class="text-subtitle1 text-grey-9">AU {{
                    formatCurrency(
                      selectedInvoice.total_tax,
                      selectedInvoice.currency_code
                    )
                  }}</q-item-label>
                </q-item-section>
              </q-item>

              <!-- Total -->
              <q-item class="col-12">
                <q-item-section>
                  <q-item-label class="text-weight-medium text-grey-7 text-caption">{{
                    $t('xero.invoices.total')
                  }}</q-item-label>
                  <q-item-label class="text-h6 text-primary text-weight-bold">AU {{
                    formatCurrency(
                      selectedInvoice.total,
                      selectedInvoice.currency_code
                    )
                  }}</q-item-label>
                </q-item-section>
              </q-item>
            </div>
          </div>

          <!-- 行項目區塊 -->
          <div class="q-pa-md q-mb-md bg-blue-grey-1 rounded-borders">
            <div class="text-subtitle1 q-mb-md text-primary">
              {{ $t('xero.invoices.lineItems') }}
            </div>
            <q-table
              :rows="selectedInvoice.line_items || []"
              :columns="lineItemColumns"
              hide-pagination
              flat
              bordered
              class="bg-white"
            >
              <template v-slot:body-cell-line_amount="props">
                <q-td :props="props">
                  AU {{
                    formatCurrency(props.value, selectedInvoice.currency_code)
                  }}
                </q-td>
              </template>
            </q-table>
          </div>
        </q-card-section>

        <q-card-actions align="between" class="col-1 full-width bg-grey-2">
          <div class="row q-gutter-sm">
            <q-btn
              color="primary"
              icon="print"
              :label="$t('printInvoice')"
              @click="handlePrintInvoice"
              :loading="printLoading"
              :disable="!canPrintInvoice"
            />
            <q-btn
              color="secondary"
              icon="email"
              :label="$t('sendEmail')"
              @click="handleSendEmail"
              :loading="emailLoading"
              :disable="!canSendEmail"
            />
          </div>
          <q-btn
            color="negative"
            icon="close"
            :label="$t('close')"
            @click="invoiceDialog = false"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { useQuasar } from 'quasar';
import { XeroApi, XeroInvoice } from '@/api/xero';
import { formatDate } from '@/utils';
import { usePageInfoStore } from '@/stores/pageInfo';
import DateRangePicker from '@/components/DateRangePicker.vue';

const router = useRouter();
const { t } = useI18n();
const $q = useQuasar();
const { setPageTitle } = usePageInfoStore();

// 設置頁面標題
setPageTitle(t('xero.invoices.title'));

// 響應式資料
const loading = ref(false);
const invoices = ref<XeroInvoice[]>([]);
const selectedInvoice = ref<XeroInvoice | null>(null);
const invoiceDialog = ref(false);
const printLoading = ref(false);
const emailLoading = ref(false);

const connectionStatus = reactive({
  connected: false,
  tenant_name: '',
});

const filters = reactive({
  status: '',
  dateRange: {
    from: '',
    to: '',
  },
});

const pagination = ref({
  sortBy: 'date',
  descending: true,
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0,
});

// 計算屬性
const statusOptions = computed(() => [
  { label: t('xero.invoices.status.draft'), value: 'DRAFT' },
  { label: t('xero.invoices.status.submitted'), value: 'SUBMITTED' },
  { label: t('xero.invoices.status.authorised'), value: 'AUTHORISED' },
  { label: t('xero.invoices.status.paid'), value: 'PAID' },
  { label: t('xero.invoices.status.voided'), value: 'VOIDED' },
]);

const columns = computed(() => [
  {
    name: 'invoice_number',
    label: t('xero.invoices.invoiceNumber'),
    field: 'invoice_number',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'type',
    label: t('xero.invoices.type.label'),
    field: 'type',
    align: 'center' as const,
    sortable: true,
  },
  {
    name: 'contact',
    label: t('xero.invoices.contact'),
    field: (row: XeroInvoice) => row.contact?.name || '',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'date',
    label: t('xero.invoices.date'),
    field: 'date',
    align: 'center' as const,
    sortable: true,
    format: (val: string) => formatDate(val),
  },
  {
    name: 'due_date',
    label: t('xero.invoices.dueDate'),
    field: 'due_date',
    align: 'center' as const,
    sortable: true,
    format: (val: string) => formatDate(val),
  },
  {
    name: 'status',
    label: t('status'),
    field: 'status',
    align: 'center' as const,
    sortable: true,
  },
  {
    name: 'total',
    label: t('xero.invoices.total'),
    field: 'total',
    align: 'right' as const,
    sortable: true,
  },
  {
    name: 'actions',
    label: t('actions'),
    field: '',
    align: 'center' as const,
  },
]);

const lineItemColumns = computed(() => [
  {
    name: 'description',
    label: t('product.label'),
    field: 'description',
    align: 'left' as const,
  },
  {
    name: 'quantity',
    label: t('quantity'),
    field: 'quantity',
    align: 'center' as const,
  },
  {
    name: 'unit_amount',
    label: t('xero.invoices.unitAmount'),
    field: 'unit_amount',
    align: 'right' as const,
    format: (val: number) =>
      'AU ' + formatCurrency(val, selectedInvoice.value?.currency_code || 'AUD'),
  },
  {
    name: 'line_amount',
    label: t('xero.invoices.lineAmount'),
    field: 'line_amount',
    align: 'right' as const,
  },
]);

// 方法
const loadConnectionStatus = async () => {
  try {
    const response = await XeroApi.getConnectionStatus();
    Object.assign(connectionStatus, response.result);
  } catch (error) {
    console.error('Failed to load connection status:', error);
  }
};

const loadInvoices = async () => {
  if (!connectionStatus.connected) return;

  loading.value = true;
  try {
    const params = {
      page: pagination.value.page,
      pageSize: pagination.value.rowsPerPage,
      status: filters.status || undefined,
      dateFrom: filters.dateRange.from || undefined,
      dateTo: filters.dateRange.to || undefined,
    };

    const response = await XeroApi.getInvoices(params);
    invoices.value = response.result?.invoices || [];
    pagination.value.page = response.result?.page || 1;
    pagination.value.rowsNumber = response.result?.total_count || 0;
  } catch (error) {
    console.error('Failed to load invoices:', error);
    invoices.value = [];
    pagination.value.rowsNumber = 0;
    $q.notify({
      position: 'top',
      type: 'negative',
      message: (error as Error)?.message || t('failed'),
    });
  } finally {
    loading.value = false;
  }
};

const onRequest = (props: any) => {
  const { page, rowsPerPage, sortBy, descending } = props.pagination;

  pagination.value.page = page;
  pagination.value.rowsPerPage = rowsPerPage;
  pagination.value.sortBy = sortBy;
  pagination.value.descending = descending;

  loadInvoices();
};

const viewInvoice = async (invoice: XeroInvoice) => {
  try {
    loading.value = true;
    const response = await XeroApi.getInvoice(invoice.invoice_id);
    selectedInvoice.value = response.result?.invoice || null;
    if (selectedInvoice.value) {
      invoiceDialog.value = true;
    } else {
      $q.notify({
        position: 'top',
        type: 'negative',
        message: t('xero.invoices.invoiceNotFound'),
      });
    }
  } catch (error) {
    console.error('Failed to load invoice details:', error);
    selectedInvoice.value = null;
    $q.notify({
      position: 'top',
      type: 'negative',
      message: (error as Error)?.message || t('failed'),
    });
  } finally {
    loading.value = false;
  }
};

const goToSetup = () => {
  router.push('/admin/dashboard/xero/setup');
};

const getStatusColor = (status: string): string => {
  const colors: Record<string, string> = {
    DRAFT: 'grey',
    SUBMITTED: 'orange',
    AUTHORISED: 'blue',
    PAID: 'positive',
    VOIDED: 'negative',
  };
  return colors[status] || 'grey';
};

const formatCurrency = (amount: number, currencyCode = 'AUD'): string => {
  return new Intl.NumberFormat('en-AU', {
    style: 'currency',
    currency: currencyCode,
  }).format(amount);
};

// 狀態相關方法
const getStatusLabel = (status: string): string => {
  const statusMap: Record<string, string> = {
    'DRAFT': t('xero.invoices.status.draft'),
    'SUBMITTED': t('xero.invoices.status.submitted'),
    'AUTHORISED': t('xero.invoices.status.authorised'),
    'PAID': t('xero.invoices.status.paid'),
    'VOIDED': t('xero.invoices.status.voided'),
  };
  return statusMap[status] || status;
};

// 計算屬性
const canPrintInvoice = computed(() => {
  if (!selectedInvoice.value) return false;
  // 只有 AUTHORISED 和 PAID 狀態的發票可以列印
  return ['AUTHORISED', 'PAID'].includes(selectedInvoice.value.status);
});

const canSendEmail = computed(() => {
  if (!selectedInvoice.value) return false;
  // 只有 AUTHORISED 和 PAID 狀態的發票可以發送郵件
  return ['AUTHORISED', 'PAID'].includes(selectedInvoice.value.status) &&
         selectedInvoice.value.contact?.name;
});

// 列印發票
const handlePrintInvoice = async () => {
  if (!selectedInvoice.value) return;

  try {
    printLoading.value = true;

    // 直接使用 XeroApi 獲取 PDF
    const blob = await XeroApi.getInvoicePDF(selectedInvoice.value.invoice_id);

    // 檢查 Blob 大小
    if (blob.size < 100) {
      throw new Error('PDF data too small, likely invalid');
    }

    // 創建 Blob URL
    const pdfUrl = URL.createObjectURL(blob);

    // 在新視窗中開啟 PDF
    const printWindow = window.open(pdfUrl, '_blank');
    if (!printWindow) {
      URL.revokeObjectURL(pdfUrl);
      throw new Error('Failed to open print window - popup blocked?');
    }

    // 等待 PDF 載入後列印
    printWindow.onload = () => {
      setTimeout(() => {
        printWindow.print();
      }, 1000);
    };

    // 清理資源
    setTimeout(() => {
      URL.revokeObjectURL(pdfUrl);
    }, 15000);

    $q.notify({
      type: 'positive',
      message: t('printInvoiceSuccess'),
      position: 'top',
    });
  } catch (error) {
    console.error('Print invoice error:', error);
    $q.notify({
      type: 'negative',
      message: (error as Error)?.message || t('printInvoiceError'),
      position: 'top',
    });
  } finally {
    printLoading.value = false;
  }
};

// 發送郵件
const handleSendEmail = async () => {
  if (!selectedInvoice.value || !selectedInvoice.value.contact?.name) return;

  // 彈出對話框讓用戶輸入郵件地址
  $q.dialog({
    title: t('sendEmail'),
    message: t('xero.invoices.enterEmailAddress'),
    prompt: {
      model: '',
      type: 'email',
      placeholder: '<EMAIL>'
    },
    cancel: true,
    persistent: true
  }).onOk(async (email: string) => {
    if (!email || !selectedInvoice.value) return;

    try {
      emailLoading.value = true;

      await XeroApi.sendInvoiceEmail(selectedInvoice.value.invoice_id, email);

      $q.notify({
        type: 'positive',
        message: t('sendEmailSuccess'),
        position: 'top',
      });
    } catch (error) {
      console.error('Send email error:', error);
      $q.notify({
        type: 'negative',
        message: (error as Error)?.message || t('sendEmailError'),
        position: 'top',
      });
    } finally {
      emailLoading.value = false;
    }
  });
};

// 生命週期
onMounted(async () => {
  await loadConnectionStatus();
  if (connectionStatus.connected) {
    await loadInvoices();
  }
});
</script>
