import { computed, ComputedRef } from 'vue';
import { i18n } from '@/boot/i18n';

export const voidedReasons = computed(() => [
  {
    label: i18n.global.t('orderVoided.reasons.outOfStock'),
    value: i18n.global.t('orderVoided.reasons.outOfStock'),
  },
  {
    label: i18n.global.t('orderVoided.reasons.duplicateOrder'),
    value: i18n.global.t('orderVoided.reasons.duplicateOrder'),
  },
  {
    label: i18n.global.t('orderVoided.reasons.incorrectInfo'),
    value: i18n.global.t('orderVoided.reasons.incorrectInfo'),
  },
  {
    label: i18n.global.t('orderVoided.reasons.customerReject'),
    value: i18n.global.t('orderVoided.reasons.customerReject'),
  },
  { label: i18n.global.t('orderVoided.reasons.other'), value: 'other' },
]);

export type OrderStatus =
  | 'pending'
  | 'processing'
  | 'completed'
  | 'cancelled'
  | 'void';

interface StatusConfig {
  color?: string; // color class for background
  textColor: string;
  icon?: string;
  label: string;
}

export const orderStatusOptions = computed(() => [
  // {
  //   label: i18n.global.t('orderStatus.pending'),
  //   value: 'pending',
  // },
  {
    label: i18n.global.t('orderStatus.processing'),
    value: 'processing',
  },
  {
    label: i18n.global.t('orderStatus.completed'),
    value: 'completed',
  },
  {
    label: i18n.global.t('orderStatus.cancelled'),
    value: 'cancelled',
  },
  {
    label: i18n.global.t('orderStatus.void'),
    value: 'void',
  },
]);

// Centralized configuration for order statuses
export const orderStatusConfig: ComputedRef<
  Record<OrderStatus | string, StatusConfig>
> = computed(() => ({
  pending: {
    textColor: 'black',
    label: i18n.global.t('orderStatus.pending'),
  },
  processing: {
    textColor: 'black',
    label: i18n.global.t('orderStatus.processing'),
  },
  completed: {
    textColor: 'black',
    label: i18n.global.t('orderStatus.completed'),
  },
  cancelled: {
    color: 'bg-red-1',
    textColor: 'red-2',
    label: i18n.global.t('orderStatus.cancelled'),
  },
  void: {
    color: 'bg-red-1',
    textColor: 'red-2',
    label: i18n.global.t('orderStatus.void'),
  },
}));

const paymentColorMap: Record<string, string> = {
  cash: 'green',
  credit: 'blue',
  paypal: 'purple',
};

const paymentLabelMap: ComputedRef<Record<string, string>> = computed(() => ({
  cash: i18n.global.t('payment.cash'),
  credit: i18n.global.t('payment.creditCard'),
  paypal: i18n.global.t('payment.paypal'),
}));

// Helper functions
export const getOrderStatusColor = (status: OrderStatus | string): string => {
  return orderStatusConfig.value[status]?.color || '';
};

export const getOrderStatusTextColor = (
  status: OrderStatus | string
): string => {
  return orderStatusConfig.value[status]?.textColor || '';
};

export const getOrderStatusLabel = (status: OrderStatus | string): string => {
  return orderStatusConfig.value[status]?.label || status;
};

export const getOrderStatusIcon = (
  status: OrderStatus | string
): string | undefined => {
  return orderStatusConfig.value[status]?.icon;
};

export const getPaymentColor = (payType: string): string => {
  return paymentColorMap[payType] || 'grey';
};

export const getPaymentLabel = (payType: string): string => {
  return paymentLabelMap.value[payType] || payType;
};
