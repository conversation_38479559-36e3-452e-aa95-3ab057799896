export default {
  app: {
    english: 'English',
    chinese: '中文',
  },
  changeLanguage: '切換語言',
  success: '成功',
  failed: '失敗',
  all: '全部',
  hint: '提示',
  optional: '選填',
  open: '開啟',
  close: '關閉',
  confirm: '確認',
  logoutConfirm: '確定要登出嗎？',
  cancel: '取消',
  add: '新增',
  disabled: '未啟用',
  confirmDelete: '確定要刪除此項目嗎？',
  error: {
    required: '此欄位為必填。',
    passwordNotMatch: '密碼不符。',
    max: '此欄位不得超過 {max} 個字元。',
    min: '此欄位至少需要 {min} 個字元。',
    maxNumber: '此欄位數值不得大於 {max}。',
    minNumber: '此欄位數值不得小於 {min}。',
    betweenNumber: '此欄位數值必須在 {min} 和 {max} 之間。',
    email: '無效的電子郵件格式。',
    noData: '無可用資料。',
    noImage: '無可用圖片。',
    invalidDate: '無效的日期格式。',
  },
  pending: '待處理',
  processing: '處理中',
  completed: '已完成',
  cancelled: '已取消',
  'wc-pending': '待處理',
  'wc-processing': '處理中',
  'wc-on-hold': '暫停',
  'wc-completed': '已完成',
  'wc-cancelled': '已取消',
  'wc-refunded': '已退款',
  'wc-failed': '失敗',
  onsite: '現場',
  online: '線上',
  orderDetail: '訂單詳情',
  attendance: {
    label: '出勤',
    history: '出勤紀錄',
  },
  payroll: {
    label: '薪資單',
    period: '薪資期間',
    payDate: '發薪日期',
    basicSalary: '基本薪資',
    hourlyRate: '時薪',
    workDays: '工作天數',
    workHours: '工作時數',
    income: '收入',
    bonus: '獎金',
    deduction: '扣款',
    grossSalary: '總收入',
    totalDeductions: '總扣款',
    netSalary: '淨收入',
    payStatus: '發薪狀態',
    paid: '已發薪',
    unpaid: '未發薪',
    status: {
      draft: '草稿',
      unpaid: '未發薪',
      paid: '已發薪',
      void: '已作廢',
    },
    // 薪資項目
    // income
    overtime_pay: '加班費',
    meal_allowance: '餐費津貼',
    transportation_allowance: '交通津貼',
    position_allowance: '職務津貼',
    performance_bonus: '績效獎金',
    perfect_attendance_bonus: '全勤獎金',
    year_end_bonus: '年終獎金',
    // deduction
    labor_insurance: '勞工保險',
    health_insurance: '健保',
    income_tax: '所得稅',
    labor_pension: '勞工退休金',
    health_pension: '健保退休金',
    late_deduction: '遲到扣款',
    absence_deduction: '缺勤扣款',
  },
  payrolls: '薪資記錄',
  salary: '薪資',
  salaryType: {
    label: '薪資類型',
    salary: '固定薪資',
    monthly: '月薪',
    hourly: '時薪',
  },
  order: {
    label: '訂單',
    list: '訂單列表',
    history: '訂單紀錄',
    salesStatistics: '銷售統計',
    void: '作廢訂單',
    qty: '訂單數量',
    count: '{count} 筆訂單',
    total: '訂單總數',
    noRecord: '無訂單紀錄',
  },
  orders: '訂單列表',
  // user
  user: {
    menu: '用戶管理',
    label: '用戶',
    all: '所有用戶',
    list: '用戶列表',
  },
  username: '用戶名',
  account: '帳號 ID',
  password: '密碼',
  confirmPassword: '確認密碼',
  datePicker: {
    from: '開始日期',
    to: '結束日期',
  },
  permissions: '權限',
  permission: {
    view_page_order: '檢視訂單頁面',
    create_order: '建立訂單',
    update_order: '更新訂單',
    delete_order: '刪除訂單',
    void_order: '作廢訂單',
    checkout_order: '結帳',
  },
  name: '名稱',
  type: '類型',
  email: {
    label: '電子郵件',
    send: '發送郵件',
    sendAll: '發送全部',
    notSet: '未設置Email',
  },
  role: '角色',
  phone: '電話',
  licensePlate: '車牌號碼',
  country: '國家',
  state: '州/省',
  city: '城市',
  zipcode: '郵遞區號',
  address: '地址',
  receiver: '收貨人',
  date: '日期',
  time: '時間',
  item: '項目',
  quantity: '數量',
  status: '狀態',
  allStatus: '所有狀態',
  note: {
    label: '備註',
    edit: '編輯備註',
  },
  group: '群組',
  catalog: {
    label: '型錄',
  },
  product: {
    label: '產品',
    name: '產品名稱',
    data: '產品資料',
    all: '所有產品',
  },
  products: '產品列表',
  addProduct: '新增產品',
  category: '類別',
  barcode: '條碼',
  description: '描述',
  shortDescription: '簡短描述',
  unit: '單位',
  price: '價格',
  salePrice: '特價',
  flexiblePrice: '彈性金額',
  productType: '產品類型',
  fixedPriceProduct: '固定金額產品',
  flexiblePriceProduct: '彈性金額產品',
  setServiceAmount: '設定服務金額',
  enterAmountLabel: '請輸入金額 (AU$)',
  cost: '成本',
  stock: '庫存',
  stockQuantity: '庫存數量',
  minStockQuantity: '最低庫存數量',
  image: '圖片',
  images: '圖片',

  serviceFee: '服務費',
  tax: '稅金',
  taxID: 'ABN',
  shippingFee: '運費',
  supplier: '供應商',

  // Attendance
  clockIn: '打卡上班',
  clock_in: '打卡上班',
  clockOut: '打卡下班',
  clock_out: '打卡下班',
  clockType: '打卡類型',
  clockTime: '打卡時間',

  createdAt: '建立時間',
  updatedAt: '更新時間',

  createUser: '建立用戶',
  createGroup: '建立群組',
  createCategory: '建立類別',
  createProduct: '建立產品',
  editProduct: '編輯產品',
  createCustomer: '建立客戶',
  editCustomer: '編輯客戶',

  save: '儲存',
  reset: '重設',
  clear: '清除',
  create: '建立',
  update: '更新',
  submit: '提交',
  delete: '刪除',
  rememberMe: '記住我',
  login: '登入',
  logout: '登出',

  // Order
  itemNum: '項目數',
  rebate: '折讓',
  discount: '折扣',
  percentageOff: '折扣 (減免 {discount}%)',
  checkout: '結帳',
  payType: '付款方式',
  payment: {
    label: '付款方式',
    stats: '付款方式統計',
    cash: '現金',
    creditCard: '信用卡',
    paypal: 'Paypal',
  },
  cash: '現金',
  creditCard: '信用卡',
  subtotal: '小計',
  total: '總計',
  cashReceived: '收到現金',

  guest: '訪客',
  customerData: '客戶資料',
  customer: {
    label: '客戶',
    all: '所有客戶',
    notSupplier: '此客戶非供應商',
  },
  customers: '客戶列表',
  checkoutSuccess: '結帳成功',
  checkoutSuccessMessage: '訂單已成功建立，是否要列印發票？',
  checkoutError: {
    noItems: '請至少選擇一項商品',
  },
  printInvoice: '列印 Xero 發票',
  printInvoiceSuccess: 'Xero 發票列印成功',
  printInvoiceError: 'Xero 發票列印失敗',
  sendEmail: '發送 Email',
  sendEmailSuccess: 'Email 發送成功',
  sendEmailError: 'Email 發送失敗',
  sendEmailRateLimitError: 'Xero 每日 Email 發送限制已達上限，請明天再試或手動從 Xero 發送',
  sendEmailRateLimitTitle: 'Email 發送限制',
  sendEmailRateLimitMessage: 'Xero 每日 Email 發送限制已達上限。發票已在 Xero 中標記為已發送，但實際 Email 未發送。\n\n解決方案：\n1. 明天再嘗試發送\n2. 登入 Xero 系統手動發送\n3. 列印發票並親自交給客戶',
  sendEmailInvalidEmailError: 'Email 地址無效，請檢查客戶的 Email 地址',
  sendEmailManualError: '請手動從 Xero 系統發送此發票',
  understood: '我了解',
  sendEmailNoCustomerOrEmail: '此訂單沒有客戶或客戶沒有 Email',
  sendEmailNotSynced: '此訂單尚未同步到 Xero，無法發送 Email',
  sendEmailSyncFailed: '此訂單同步到 Xero 失敗，無法發送 Email',
  orderNotLoaded: '訂單資料尚未載入',
  cannotEmailVoidedOrder: '已作廢的訂單無法發送 Email',
  orderNumber: '訂單號碼',

  unknown: {
    customer: '無客戶資料',
  },

  search: {
    label: '搜尋',
    customer: '輸入客戶名稱、電話、ABN',
    product: '輸入產品名稱或條碼',
    order: '輸入訂單編號',
  },

  punch: '打卡',
  history: '歷史記錄',
  latestPunch: '最近打卡',
  changeUser: '更換使用者',

  orderNo: '訂單編號',
  dateAt: '日期',
  orderDate: '訂單日期',
  void: '作廢',
  reason: '原因',
  voidReason: '作廢原因',

  orderVoided: {
    title: '訂單已作廢',
    reasons: {
      outOfStock: '庫存不足',
      duplicateOrder: '重複訂單',
      incorrectInfo: '資訊不正確',
      customerChangedMind: '客戶改變主意',
      customerReject: '客戶取消',
      other: '其他',
    },
    confirm: '確定要作廢此訂單嗎？',
    reasonRequired: '請選擇一個原因。',
    otherReasonRequired: '請輸入原因。',
  },
  orderStatus: {
    label: '訂單狀態',
    pending: '待處理',
    processing: '處理中',
    completed: '已完成',
    cancelled: '已取消',
    void: '已作廢',
  },

  inventory: {
    label: '庫存',
    stockHistory: '庫存紀錄',
    stock_in: {
      label: '入庫',
      caption: '增加產品庫存',
    },
    return: {
      label: '退貨',
      caption: '客戶退貨或退回給供應商',
    },
    scrap: {
      label: '報廢',
      caption: '從庫存中移除',
    },
    stocktaking: {
      label: '盤點',
      caption: '清點店內庫存',
    },
    transaction: {
      sale: '銷售',
      purchase: '採購',
      stock_in: '入庫',
      stock_out: '出庫',
      stocktaking: '盤點',
      scrap: '報廢',
      return_in: '退貨入庫',
      return_out: '退貨出庫',
    },
    returnType: {
      label: '退貨類型',
      customer_return: '客戶退貨',
      supplier_return: '退回供應商',
    },
    beforeQuantity: '變更前庫存',
    afterQuantity: '變更後庫存',
    diffQuantity: '差異量',
  },

  actions: '操作',

  wpOrder: {
    actions: {
      complete: '完成訂單',
      cancel: '取消訂單',
      refund: '退款',
      processing: '更改為處理中',
    },
    title: {
      complete: '完成訂單',
      cancel: '取消訂單',
      refund: '退款',
      processing: '處理中',
    },
  },

  orderItem: {
    is_free: '贈品',
  },

  vip: {
    label: 'VIP',
    expiryDate: 'VIP 到期日',
  },

  year: {
    label: '年',
  },
  month: {
    label: '月',
  },

  barcodeScanner: {
    ready: '條碼掃描器就緒',
    scanning: '掃描中...',
    notFound: '找不到商品',
    notFoundMessage: '找不到條碼為 {barcode} 的商品',
    scanCompleted: '掃描完成',
  },
  added: '已加入',

  system: '系統設定',
  updateService: {
    updateNotification: {
      title: '系統更新通知',
      newVersion: '發現新版本',
      currentVersion: '目前版本',
      buildTime: '建置時間',
      recommendUpdate: '建議立即更新以獲得最佳體驗',
      doNotClose: '更新期間請勿關閉瀏覽器視窗',
      updateLater: '稍後提醒',
      updateNow: '立即更新',
      laterReminder: '💡 稍後將再次提醒您更新',
    },
    updateProcess: {
      updating: '正在更新系統，請稍候...',
      success: '系統更新完成，即將重新載入',
      failed: '更新失敗，將嘗試強制重新整理',
      forceRefresh: '強制重新整理中...',
    },
  },
  xero: {
    menu: {
      setup: '設定',
      invoices: '發票',
    },
    setup: {
      title: 'Xero API 設定',
      connectionStatus: {
        connected: '已連接到 Xero 帳號: {tenantName}',
        tokenExpiry: 'Token 到期時間: {expiryDate}',
        notConnected: '尚未連接到 Xero 帳號',
        disconnect: '中斷連接',
        refreshToken: '刷新Token',
      },
      form: {
        clientId: 'Client ID',
        clientSecret: 'Client Secret',
        redirectUri: 'Redirect URI',
        scopes: '權限範圍',
        scopesHint: '預設: accounting.transactions accounting.contacts',
        scopesPlaceholder: 'accounting.transactions accounting.contacts',
        saveConfig: '儲存設定',
        connectXero: '連接 Xero',
      },
      validation: {
        clientIdRequired: 'Client ID為必填',
        clientSecretRequired: 'Client Secret為必填',
        redirectUriRequired: 'Redirect URI為必填',
      },
      dialog: {
        disconnectTitle: '確認中斷連接',
        disconnectMessage: '確定要中斷與Xero的連接嗎？',
      },
      error: {
        stateStorageFailed: '無法保存認證狀態，請重新嘗試。',
      },
    },
    redirect: {
      processing: '正在處理 Xero 認證...',
      success: {
        title: 'Xero 連接成功！',
        connectedTo: '已成功連接到: {tenantName}',
        backToSettings: '返回設定頁面',
        notification: 'Xero連接成功！',
      },
      error: {
        title: '連接失敗',
        retry: '重新嘗試',
        clearState: '清除狀態並重試',
        messages: {
          authError: '認證錯誤: {error}',
          missingParams: '缺少必要的認證參數',
          invalidState: '無效的state參數，可能存在安全風險',
          noSavedState: '找不到已保存的state，請重新嘗試連接',
          unknownOrg: '未知組織',
        },
      },
      debug: {
        title: '調試信息',
        stateCleared: '認證狀態已成功清除',
      },
    },
    invoices: {
      title: 'Xero 發票',
      notConnected: '尚未連接到 Xero，請先配置連接。',
      goToSetup: '前往設定',
      dateFrom: '開始日期',
      dateTo: '結束日期',
      invoiceNumber: '發票號碼',
      contact: '聯絡人',
      date: '日期',
      dueDate: '到期日',
      subTotal: '小計',
      totalTax: '總稅額',
      total: '總計',
      invoiceDetails: '發票詳情',
      basicInfo: '基本資料',
      lineItems: '項目明細',
      unitAmount: '單價',
      lineAmount: '金額',
      enterEmailAddress: '請輸入郵件地址',
      invoiceNotFound: '找不到發票',
      type: {
        label: '類型',
        accrec: '銷售發票',
        accpay: '採購發票',
      },
      status: {
        draft: '草稿',
        submitted: '已提交',
        authorised: '已授權',
        paid: '已付款',
        voided: '已作廢',
        deleted: '已刪除',
      },
    },
    syncStatus: 'Xero 同步狀態',
    notSynced: '未同步',
    syncPending: '待同步',
    syncing: '同步中',
    syncSuccess: '同步成功',
    syncFailed: '同步失敗',
    voidedInvoice: '已作廢發票',
  },
};
