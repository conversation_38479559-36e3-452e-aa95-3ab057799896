<template>
  <q-dialog v-model="visible" class="card-dialog" no-refocus>
    <q-card class="column">
      <!-- close -->
      <q-card-section class="col-1 q-py-none">
        <div class="row q-mt-sm">
          <div class="text-h5 text-bold">
            {{ t('orderDetail') }}
          </div>
          <q-space />
          <q-btn
            type="button"
            icon="close"
            flat
            round
            dense
            @click="visible = false"
          />
        </div>
      </q-card-section>

      <q-card-section class="col-10 text-h6 q-py-sm">
        <q-scroll-area class="full-height">
          <!-- data -->
          <div class="row q-mb-sm">
            <!-- order No -->
            <q-item class="col-12">
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('orderNo') }}
              </q-item-section>
              <q-item-section>
                {{ wcOrder?.id }}
              </q-item-section>
            </q-item>
            <!-- order date -->
            <q-item class="col-12 col-md-6">
              <q-item-section side>
                <q-icon name="event" size="sm" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('orderDate') }}
              </q-item-section>
              <q-item-section>
                {{ formatDate(wcOrder?.date_created, 'YYYY-MM-DD HH:mm') }}
              </q-item-section>
            </q-item>
            <!-- customer -->
            <q-item class="col-12 col-md-6">
              <q-item-section side>
                <q-icon name="person" size="sm" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('customer.label') }}
              </q-item-section>
              <q-item-section>
                {{ wcOrder?.billing_first_name }}
                {{ wcOrder?.billing_last_name }}
              </q-item-section>
            </q-item>
            <!-- email -->
            <q-item class="col-12">
              <q-item-section side>
                <q-icon name="email" size="sm" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('email.label') }}
              </q-item-section>
              <q-item-section>
                {{ wcOrder?.billing_email }}
              </q-item-section>
            </q-item>
            <!-- phone -->
            <q-item class="col-12">
              <q-item-section side>
                <q-icon name="phone" size="sm" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('phone') }}
              </q-item-section>
              <q-item-section>
                {{ wcOrder?.billing_phone }}
              </q-item-section>
            </q-item>
            <!-- status -->
            <q-item class="col-12">
              <q-item-section side>
                <q-icon name="check_circle" size="sm" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('status') }}
              </q-item-section>
              <q-item-section>
                <span v-if="wcOrder?.status">
                  {{ t(wcOrder.status) }}
                </span>
              </q-item-section>
            </q-item>
            <!-- payment method -->
            <q-item class="col-12 col-md-6">
              <q-item-section side>
                <q-icon name="payment" size="sm" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('payment.label') }}
              </q-item-section>
              <q-item-section>
                {{ wcOrder?.payment_method_title }}
              </q-item-section>
            </q-item>
            <!-- total -->
            <q-item class="col-12 col-md-6">
              <q-item-section side>
                <q-icon name="attach_money" size="sm" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('total') }}
              </q-item-section>
              <q-item-section>
                AU$ {{ formatNumber(wcOrder?.total, 2) }}
              </q-item-section>
            </q-item>

            <q-item class="col-12">
              <q-item-section>
                <q-separator color="black" size="2px" />
              </q-item-section>
            </q-item>

            <!-- 收貨人 -->
            <q-item class="col-12">
              <q-item-section side>
                <q-icon name="person" size="sm" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('receiver') }}
              </q-item-section>
              <q-item-section>
                {{ wcOrder?.shipping_first_name }}
                {{ wcOrder?.shipping_last_name }}
              </q-item-section>
            </q-item>

            <!-- address -->
            <q-item class="col-12">
              <q-item-section side>
                <q-icon name="location_on" size="sm" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('address') }}
              </q-item-section>
              <q-item-section>
                {{ wcOrder?.shipping_address_1 }},
                {{ wcOrder?.shipping_city }},
                {{ wcOrder?.shipping_state }},
                {{ wcOrder?.shipping_postcode }},
                {{ wcOrder?.shipping_country }}
              </q-item-section>
            </q-item>
          </div>
          <!-- items -->
          <q-list bordered separator>
            <q-item class="bg-grey-3">
              <q-item-section class="text-bold">
                {{ t('product.label') }}
              </q-item-section>

              <q-item-section class="text-bold" side>
                {{ t('price') }}
              </q-item-section>
            </q-item>
            <!-- 商品列表 -->
            <q-item v-for="item in wcOrder?.line_items" :key="item.name">
              <q-item-section>
                {{ item.name }}
              </q-item-section>
              <q-item-section class="text-subtitle1" side>
                x {{ item.quantity }}
              </q-item-section>
              <q-item-section class="text-bold" side>
                AU$ {{ formatNumber(item.total, 2) }}
              </q-item-section>
            </q-item>
            <!-- 運費 -->
            <q-item v-if="wcOrder?.shipping_total">
              <q-item-section>
                {{ t('shippingFee') }}
              </q-item-section>
              <q-item-section class="text-bold" side>
                AU$ {{ formatNumber(wcOrder?.shipping_total, 2) }}
              </q-item-section>
            </q-item>
            <!-- 總計 -->
            <q-item class="bg-grey-3">
              <q-item-section>
                <q-item-label class="text-bold">{{ t('total') }}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-item-label class="text-bold">
                  AU$ {{ formatNumber(wcOrder?.total, 2) }}
                </q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </q-scroll-area>
      </q-card-section>

      <!-- actions -->
      <q-card-actions align="between" class="col-1 bg-grey-2 q-pa-sm">
        <q-btn
          color="red"
          icon="delete"
          :label="t('wpOrder.actions.cancel')"
          no-caps
          @click="cancelWpOrder"
          :loading="isLoading"
          v-if="wcOrder?.status != 'wc-cancelled'"
        />
        <q-btn
          color="positive"
          :label="t('wpOrder.actions.processing')"
          no-caps
          @click="processWpOrder"
          :loading="isLoading"
          v-if="wcOrder?.status != 'wc-processing'"
        />
        <q-btn
          color="positive"
          :label="t('wpOrder.actions.complete')"
          no-caps
          @click="completeWpOrder"
          :loading="isLoading"
          v-if="wcOrder?.status != 'wc-completed'"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { OrderApi, WCOrder } from '@/api/order';
import { formatDate, formatNumber, useDialog } from '@/utils';

const { t } = useI18n();
const dialog = useDialog();

const props = defineProps<{
  modelValue: boolean;
  orderID: number;
}>();

const emit = defineEmits(['update:modelValue', 'refresh']);

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      getData();
    }
  }
);

const isLoading = ref(false);
const wcOrder = ref<WCOrder>();
const getData = async () => {
  try {
    isLoading.value = true;

    const response = await OrderApi.getWCOrder(props.orderID);

    wcOrder.value = response.result;
    console.log(wcOrder.value);
  } finally {
    isLoading.value = false;
  }
};

const processWpOrder = () => {
  dialog.showMessage({
    title: t('wpOrder.title.processing'),
    message: '',
    timeout: 0,
    persistent: true,
    ok: async () => {
      try {
        isLoading.value = true;

        await OrderApi.updateWCOrderStatus(props.orderID, 'processing');
      } finally {
        isLoading.value = false;
        visible.value = false;
        emit('refresh');
      }
    },
  });
};

const completeWpOrder = () => {
  dialog.showMessage({
    title: t('wpOrder.title.complete'),
    message: '',
    timeout: 0,
    persistent: true,
    ok: async () => {
      try {
        isLoading.value = true;

        await OrderApi.updateWCOrderStatus(props.orderID, 'completed');
      } finally {
        isLoading.value = false;
        visible.value = false;
        emit('refresh');
      }
    },
  });
};

const cancelWpOrder = async () => {
  dialog.showMessage({
    title: t('wpOrder.title.cancel'),
    message: '',
    timeout: 0,
    persistent: true,
    ok: async () => {
      try {
        isLoading.value = true;

        await OrderApi.updateWCOrderStatus(props.orderID, 'cancelled');
      } finally {
        isLoading.value = false;
        visible.value = false;
        emit('refresh');
      }
    },
  });
};
</script>
