# WooCommerce 訂單整合測試指南

## 功能概述

已成功將 WordPress WooCommerce 訂單功能整合到當前後端 API 中，替代了原本的外部 PHP API 調用。

## 新增的 API 端點

### 1. 獲取歷史訂單
- **端點**: `GET /api/v1/wc-orders/history`
- **說明**: 獲取所有歷史訂單列表
- **回應格式**: 
```json
{
  "success": true,
  "orders": [
    {
      "id": 123,
      "date_created": "2024-01-01 10:00:00",
      "status": "wc-completed",
      "total": 100.50,
      "payment_method_title": "Credit Card",
      "customer_email": "<EMAIL>",
      "customer_name": "<PERSON>"
    }
  ]
}
```

### 2. 獲取待處理訂單
- **端點**: `GET /api/v1/wc-orders/pending`
- **說明**: 獲取所有待處理訂單列表
- **回應格式**: 同上

### 3. 獲取訂單詳細資料
- **端點**: `GET /api/v1/wc-orders/{id}`
- **說明**: 獲取指定訂單的詳細資料
- **回應格式**:
```json
{
  "success": true,
  "order": {
    "id": 123,
    "status": "wc-completed",
    "total": 100.50,
    "billing_first_name": "John",
    "billing_last_name": "Doe",
    "billing_email": "<EMAIL>",
    "line_items": [
      {
        "id": 1,
        "name": "Product Name",
        "quantity": 2,
        "total": 50.00
      }
    ]
  }
}
```

### 4. 更新訂單狀態
- **端點**: `PATCH /api/v1/wc-orders/{id}/status`
- **請求體**:
```json
{
  "status": "wc-processing"
}
```
- **回應格式**:
```json
{
  "success": true,
  "message": "Order status updated successfully"
}
```

## 測試步驟

### 1. 啟動後端服務
```bash
cd backend
go run main.go
```

### 2. 測試 API 端點
使用 Postman 或 curl 測試以下端點：

```bash
# 獲取歷史訂單
curl -X GET "http://localhost:8080/api/v1/wc-orders/history" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 獲取待處理訂單
curl -X GET "http://localhost:8080/api/v1/wc-orders/pending" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 獲取訂單詳細資料
curl -X GET "http://localhost:8080/api/v1/wc-orders/123" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 更新訂單狀態
curl -X PATCH "http://localhost:8080/api/v1/wc-orders/123/status" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"status": "wc-processing"}'
```

### 3. 前端測試
啟動前端應用並測試線上訂單頁面功能是否正常。

## 資料庫要求

確保 WordPress 資料庫包含以下表格：
- `wp_posts` (訂單基本資料)
- `wp_postmeta` (訂單元數據)
- `wp_woocommerce_order_items` (訂單項目)
- `wp_woocommerce_order_itemmeta` (訂單項目元數據)

## 配置檢查

確認 `.env` 文件包含正確的 WordPress 資料庫配置：
```
WP_DB_NAME=your_wordpress_database_name
```

## 已完成的整合

✅ 建立 WcOrderRepository - 處理 WordPress 資料庫操作
✅ 建立 WcOrderService - 處理業務邏輯
✅ 建立 WcOrderController - 提供 API 端點
✅ 更新路由配置 - 註冊新的 API 路由
✅ 更新前端 API 調用 - 從外部 PHP API 改為內部 API

## 注意事項

1. 所有 API 端點都需要有效的認證 token
2. 訂單狀態必須是有效的 WooCommerce 狀態（如 wc-pending, wc-processing, wc-completed 等）
3. 確保 WordPress 資料庫連接正常
4. 前端頁面應該能正常顯示訂單資料並支持狀態更新功能
